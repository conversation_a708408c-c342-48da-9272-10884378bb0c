<?php

namespace App\Model;

use Illuminate\Database\Eloquent\Model;

class SystemScheduleModels extends Model
{
    //1、关联的数据表
    public $table = 'plc_system_schedule';

    //2、主键
    public $primaryKey = 'id';

    // 3、允许批量操作的字段
    public $fillable = [
        'sn',
        'monday',
        'tuesday',
        'wednesday',
        'thursday',
        'friday',
        'saturday',
        'sunday'
    ];

    // 4、是否维护created_at和updated_at字段
    public $timestamps = true;

    /**
     * 字段类型转换
     */
    protected $casts = [
        'monday' => 'boolean',
        'tuesday' => 'boolean',
        'wednesday' => 'boolean',
        'thursday' => 'boolean',
        'friday' => 'boolean',
        'saturday' => 'boolean',
        'sunday' => 'boolean',
    ];

    /**
     * 星期映射
     */
    const WEEKDAYS = [
        1 => 'monday',
        2 => 'tuesday',
        3 => 'wednesday',
        4 => 'thursday',
        5 => 'friday',
        6 => 'saturday',
        7 => 'sunday'
    ];

    /**
     * 中文星期映射
     */
    const WEEKDAYS_CN = [
        'monday' => '周一',
        'tuesday' => '周二',
        'wednesday' => '周三',
        'thursday' => '周四',
        'friday' => '周五',
        'saturday' => '周六',
        'sunday' => '周日'
    ];

    /**
     * 根据设备编号获取调度配置
     */
    public static function getByDeviceSn($sn)
    {
        return self::where('sn', $sn)->first();
    }

    /**
     * 设置设备的周调度
     */
    public static function setSchedule($sn, $schedule)
    {
        return self::updateOrCreate(
            ['sn' => $sn],
            $schedule
        );
    }

    /**
     * 检查今天是否启用
     */
    public function isTodayEnabled()
    {
        $today = date('N'); // 1=周一, 7=周日
        $weekdayField = self::WEEKDAYS[$today] ?? null;
        
        if (!$weekdayField) {
            return false;
        }
        
        return $this->$weekdayField;
    }

    /**
     * 检查指定星期几是否启用
     */
    public function isWeekdayEnabled($weekday)
    {
        $weekdayField = self::WEEKDAYS[$weekday] ?? null;
        
        if (!$weekdayField) {
            return false;
        }
        
        return $this->$weekdayField;
    }

    /**
     * 获取启用的星期列表
     */
    public function getEnabledWeekdays()
    {
        $enabled = [];
        foreach (self::WEEKDAYS as $num => $field) {
            if ($this->$field) {
                $enabled[] = [
                    'number' => $num,
                    'field' => $field,
                    'name_cn' => self::WEEKDAYS_CN[$field]
                ];
            }
        }
        return $enabled;
    }

    /**
     * 获取格式化的调度信息
     */
    public function getFormattedSchedule()
    {
        $schedule = [];
        foreach (self::WEEKDAYS as $num => $field) {
            $schedule[$field] = [
                'enabled' => $this->$field,
                'name_cn' => self::WEEKDAYS_CN[$field],
                'weekday_number' => $num
            ];
        }
        return $schedule;
    }

    /**
     * 批量设置星期启用状态
     */
    public function setWeekdays($weekdays)
    {
        foreach (self::WEEKDAYS as $num => $field) {
            $this->$field = in_array($num, $weekdays);
        }
        return $this->save();
    }

    /**
     * 获取启用天数统计
     */
    public function getEnabledDaysCount()
    {
        $count = 0;
        foreach (self::WEEKDAYS as $field) {
            if ($this->$field) {
                $count++;
            }
        }
        return $count;
    }
}
