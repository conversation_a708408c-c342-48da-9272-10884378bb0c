<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class UpdateLanguageCodesInPlcUserTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // 更新现有的语言代码
        DB::table('plc_user')->where('language', 'zh_CN')->update(['language' => 'CN']);
        DB::table('plc_user')->where('language', 'en')->update(['language' => 'EN']);
        DB::table('plc_user')->where('language', 'zh_TW')->update(['language' => 'TC']);
        
        // 如果language字段为空或null，设置为默认值CN
        DB::table('plc_user')->whereNull('language')->update(['language' => 'CN']);
        DB::table('plc_user')->where('language', '')->update(['language' => 'CN']);
        
        // 如果language字段不存在，添加该字段
        if (!Schema::hasColumn('plc_user', 'language')) {
            Schema::table('plc_user', function (Blueprint $table) {
                $table->string('language', 10)->default('CN')->comment('用户语言设置')->after('email');
            });
        } else {
            // 如果字段存在，更新默认值
            Schema::table('plc_user', function (Blueprint $table) {
                $table->string('language', 10)->default('CN')->comment('用户语言设置')->change();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // 回滚语言代码更新
        DB::table('plc_user')->where('language', 'CN')->update(['language' => 'zh_CN']);
        DB::table('plc_user')->where('language', 'EN')->update(['language' => 'en']);
        DB::table('plc_user')->where('language', 'TC')->update(['language' => 'zh_TW']);
        
        // 恢复默认值
        if (Schema::hasColumn('plc_user', 'language')) {
            Schema::table('plc_user', function (Blueprint $table) {
                $table->string('language', 10)->default('zh_CN')->comment('用户语言设置')->change();
            });
        }
    }
}
