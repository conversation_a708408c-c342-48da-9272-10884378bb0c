<?php

namespace App\Http\Controllers\Home;

use App\Http\Controllers\Controller;
use App\Services\EmailNotificationService;
use Illuminate\Support\Facades\Log;

class CodeController extends Controller
{
    protected $account = 'OT00724';
    protected $password = 'anwd87v4';

    public function sendCode($info) //发送验证码
    {

        $phone = $info['phone'];
        $device = $info['devicename'];
        $contents = $info['contents'];

        $content = "【蹄伺令(青岛)创新科技有限公司】您的" . $device . "：" . $contents . ",请您及时处理";
        $body = [
            'action' => 'send',
            'account' => $this->account,
            'password' => $this->password,
            'mobile' => $phone,
            'content' => $content,
        ];
        // dd($body);
        $url = 'https://dx.ipyy.net/smsJson.aspx';

        $res = $this->httpRequest($url, $body);
        return response()->json(['result' => 1, 'message' => '成功']);
    }

    function httpRequest($url, $postData = [])
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        if (!empty($postData)) {
            // 设置POST请求的选项
            curl_setopt($ch, CURLOPT_POST, true);
            // 设置POST请求传递的数据
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        }
        // 注意:如果发送https协议请求，禁用环境的的SSL证书认证。
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        // 设置请求头信息
        $header = ['Accept-Charset: utf-8'];
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        $result = curl_exec($ch);
        curl_close($ch);
        return $result;
    }

    /**
     * @param array $info 包含phone, email, devicename, contents, notification_method的数组
     * @return array
     */
    public function sendSmartNotification($info)
    {
        $emailService = new EmailNotificationService();
        $notification_method = $info['notification_method'];

        if ($notification_method == 0) {
            //发送短信
            return $this->sendCode($info);
        } else {
            //发送邮件
            if (isset($info['email']) && $emailService->isValidEmail($info['email'])) {
                $success = $emailService->sendDeviceAlert(
                    $info['email'],
                    $info['devicename'],
                    $info['contents']
                );

                if ($success) {
                    return response()->json(['result' => 1, 'message' => '邮件发送成功']);
                } else {
                    // 邮件发送失败，尝试发送短信作为备用方案
                   Log::info('邮件发送失败', $info);
                }
            } else {
                // 没有有效邮箱，发送短信
                Log::info('没有有效邮箱地址，使用短信发送', $info);
            }
        }
    }
}
