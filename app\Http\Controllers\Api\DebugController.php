<?php
namespace App\Http\Controllers\Api;

use App\Http\Controllers\Api\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Hash;
use Exception;
use GuzzleHttp;
use QrCode;
class DebugController extends Controller
{
    /**
     * crc16计算
     * 传入字符串格式：001624180101
     * 返回值格式：[高8位,低8位]
     */
    // function crc16($string)
    // {
    //     $string = pack('H*', $string);
    //     $crc = 0xFFFF;
    //     for ($x = 0; $x < strlen($string); $x++) {
    //         $crc = $crc ^ ord($string[$x]);
    //         for ($y = 0; $y < 8; $y++) {
    //             if (($crc & 0x0001) == 0x0001) {
    //                 $crc = (($crc >> 1) ^ 0xA001);
    //             } else {
    //                 $crc = $crc >> 1;
    //             }
    //         }
    //     }

    //     $high8 = str_pad(dechex(floor($crc / 256)), 2, '0', STR_PAD_LEFT);
    //     $low8 = str_pad(dechex($crc % 256), 2, '0', STR_PAD_LEFT);
    //     return [$high8, $low8];
    // }

    public function modbus(Request $request)
    {
         //var_dump(crc16("\x01\x03\x00\x00\x00\x78"));
        var_dump(base64_encode("\x01\x03\x04\x05"));
        //"\x01\x03\x00\x00\x00\x78\x45\xE8"
        $bytearry=[];
        $bytearry[0]=0x01;
        $bytearry[1]=3;
        $bytearry[2]=196;//0xc4;
        $bytearry[3]=5;
        //var_dump(unpack("C*",pack("C*", ...$bytearry)));
        var_dump(base64_encode(pack("C*", ...$bytearry)));
    }
}
?>