<?php

namespace App\Services;

use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class EmailNotificationService
{
    /**
     * 发送设备报警邮件
     * @param string $email 收件人邮箱
     * @param string $deviceName 设备名称
     * @param string $alertContent 报警内容
     * @return bool
     */
    public function sendDeviceAlert($email, $deviceName, $alertContent)
    {
        try {
            $subject = '设备报警通知 - ' . $deviceName;
            $message = "【蹄伺令(青岛)创新科技有限公司】\n\n";
            $message .= "您的设备出现了需要关注的情况，请及时处理。\n\n";
            $message .= "设备编号：{$deviceName}\n";
            $message .= "报警内容：{$alertContent}\n\n";
            $message .= "报警时间：" . date('Y-m-d H:i:s') . "\n\n";
            $message .= "请您及时登录系统查看详细信息并处理相关问题。\n\n";
            $message .= "此邮件为系统自动发送，请勿回复。";

            Mail::raw($message, function ($mail) use ($email, $subject) {
                $mail->to($email)->subject($subject);
            });

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 验证邮箱格式
     * @param string $email
     * @return bool
     */
    public function isValidEmail($email)
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * 发送测试邮件
     * @param string $email
     * @return bool
     */
    public function sendTestEmail($email)
    {
        try {
            Mail::raw('这是一封测试邮件，用于验证邮件配置是否正确。', function ($message) use ($email) {
                $message->to($email)
                        ->subject('邮件配置测试');
            });
            
            return true;
        } catch (\Exception $e) {
            Log::error("测试邮件发送失败: " . $e->getMessage());
            return false;
        }
    }
}
