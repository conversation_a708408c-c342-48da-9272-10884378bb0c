<?php

namespace App\Services;

use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class IpLocationService
{
    protected $client;

    public function __construct()
    {
        $this->client = new Client();
    }

    /**
     * 检测IP是否为中国大陆地址
     * @param string $ip
     * @return bool
     */
    public function isMainlandChina(Request $request, $ip = null)
    {
        // 如果没有提供IP，获取客户端真实IP
        if (!$ip) {
            $ip = $this->getClientIp($request);
        }

        // 检查是否为内网IP
        if ($this->isPrivateIp($ip)) {
            // 内网IP默认认为是中国大陆
            return true;
        }

        try {
            // 使用免费的IP地理位置API
            $response = $this->client->get("http://ip-api.com/json/{$ip}?fields=status,country,countryCode");
            $data = json_decode($response->getBody()->getContents(), true);

            if ($data['status'] === 'success') {
                // 检查是否为中国
                return $data['countryCode'] === 'CN';
            }
        } catch (\Exception $e) {
            // Log::error('IP地理位置检测失败: ' . $e->getMessage());
            // 如果API调用失败，默认返回true（使用短信）
            return true;
        }

        // 默认返回true（使用短信）
        return true;
    }

    /**
     * 获取客户端真实IP地址
     * @return string
     */
    public function getClientIp(Request $request)
    {
        // 优先级顺序检查各种可能的IP头
        $ipHeaders = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_X_REAL_IP',            // Nginx proxy
            'HTTP_X_FORWARDED_FOR',      // 标准代理头
            'HTTP_X_FORWARDED',          // 代理头变体
            'HTTP_X_CLUSTER_CLIENT_IP',  // 集群
            'HTTP_FORWARDED_FOR',        // 代理头变体
            'HTTP_FORWARDED',            // RFC 7239
            'HTTP_CLIENT_IP',            // 客户端IP头
            'REMOTE_ADDR'                // 直连IP（最后选择）
        ];

        foreach ($ipHeaders as $header) {
            $ip = $request->server($header);

            if (!empty($ip) && $ip !== 'unknown') {
                // 处理多个IP的情况（X-Forwarded-For可能包含多个IP）
                if (strpos($ip, ',') !== false) {
                    $ips = explode(',', $ip);
                    $ip = trim($ips[0]); // 取第一个IP
                }

                // 验证IP格式
                if ($this->isValidIp($ip)) {
                    return $ip;
                }
            }
        }

        // 如果都没有找到有效IP，返回默认值
        return '0.0.0.0';
    }

    /**
     * 验证IP地址是否有效
     *
     * @param string $ip
     * @return bool
     */
    private function isValidIp(string $ip): bool
    {
        return filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false
            || filter_var($ip, FILTER_VALIDATE_IP) !== false;
    }

    /**
     * 检查是否为内网IP
     * @param string $ip
     * @return bool
     */
    private function isPrivateIp($ip)
    {
        return !filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE);
    }

    /**
     * 获取IP的详细地理信息
     * @param string $ip
     * @return array
     */
    public function getLocationInfo(Request $request, $ip = null)
    {
        if (!$ip) {
            $ip = $this->getClientIp($request);
        }
        try {
            $response = $this->client->get("http://ip-api.com/json/{$ip}");
            $data = json_decode($response->getBody()->getContents(), true);

            if ($data['status'] === 'success') {
                return [
                    'ip' => $ip,
                    'country' => $data['country'] ?? '',
                    'countryCode' => $data['countryCode'] ?? '',
                    'region' => $data['regionName'] ?? '',
                    'city' => $data['city'] ?? '',
                    'timezone' => $data['timezone'] ?? '',
                ];
            }
        } catch (\Exception $e) {
            Log::error('获取IP地理信息失败: ' . $e->getMessage());
        }

        return [
            'ip' => $ip,
            'country' => '未知',
            'countryCode' => '',
            'region' => '',
            'city' => '',
            'timezone' => '',
        ];
    }
}
