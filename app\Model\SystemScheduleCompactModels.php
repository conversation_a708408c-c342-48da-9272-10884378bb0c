<?php

namespace App\Model;

use Illuminate\Database\Eloquent\Model;

class SystemScheduleCompactModels extends Model
{
    //1、关联的数据表
    public $table = 'plc_system_schedule_compact';

    //2、主键
    public $primaryKey = 'id';

    // 3、允许批量操作的字段
    public $fillable = [
        'sn',
        'weekdays'
    ];

    // 4、是否维护created_at和updated_at字段
    public $timestamps = true;

    /**
     * 字段类型转换
     */
    protected $casts = [
        'weekdays' => 'integer',
    ];

    /**
     * 星期位掩码常量
     */
    const WEEKDAY_BITS = [
        1 => 1,    // 周一 = 2^0 = 1
        2 => 2,    // 周二 = 2^1 = 2
        3 => 4,    // 周三 = 2^2 = 4
        4 => 8,    // 周四 = 2^3 = 8
        5 => 16,   // 周五 = 2^4 = 16
        6 => 32,   // 周六 = 2^5 = 32
        7 => 64    // 周日 = 2^6 = 64
    ];

    /**
     * 中文星期映射
     */
    const WEEKDAYS_CN = [
        1 => '周一',
        2 => '周二',
        3 => '周三',
        4 => '周四',
        5 => '周五',
        6 => '周六',
        7 => '周日'
    ];

    /**
     * 根据设备编号获取调度配置
     */
    public static function getByDeviceSn($sn)
    {
        return self::where('sn', $sn)->first();
    }

    /**
     * 设置设备的周调度
     */
    public static function setSchedule($sn, $weekdays)
    {
        // $weekdays 可以是数组 [1,2,3,4,5] 或者位掩码整数
        if (is_array($weekdays)) {
            $weekdays = self::arrayToBitmask($weekdays);
        }
        
        return self::updateOrCreate(
            ['sn' => $sn],
            ['weekdays' => $weekdays]
        );
    }

    /**
     * 将星期数组转换为位掩码
     */
    public static function arrayToBitmask($weekdayArray)
    {
        $bitmask = 0;
        foreach ($weekdayArray as $weekday) {
            if (isset(self::WEEKDAY_BITS[$weekday])) {
                $bitmask |= self::WEEKDAY_BITS[$weekday];
            }
        }
        return $bitmask;
    }

    /**
     * 将位掩码转换为星期数组
     */
    public function bitmaskToArray()
    {
        $weekdays = [];
        foreach (self::WEEKDAY_BITS as $weekday => $bit) {
            if ($this->weekdays & $bit) {
                $weekdays[] = $weekday;
            }
        }
        return $weekdays;
    }

    /**
     * 检查今天是否启用
     */
    public function isTodayEnabled()
    {
        $today = date('N'); // 1=周一, 7=周日
        return $this->isWeekdayEnabled($today);
    }

    /**
     * 检查指定星期几是否启用
     */
    public function isWeekdayEnabled($weekday)
    {
        $bit = self::WEEKDAY_BITS[$weekday] ?? 0;
        return ($this->weekdays & $bit) > 0;
    }

    /**
     * 启用指定星期
     */
    public function enableWeekday($weekday)
    {
        $bit = self::WEEKDAY_BITS[$weekday] ?? 0;
        if ($bit > 0) {
            $this->weekdays |= $bit;
        }
        return $this;
    }

    /**
     * 禁用指定星期
     */
    public function disableWeekday($weekday)
    {
        $bit = self::WEEKDAY_BITS[$weekday] ?? 0;
        if ($bit > 0) {
            $this->weekdays &= ~$bit;
        }
        return $this;
    }

    /**
     * 获取启用的星期列表
     */
    public function getEnabledWeekdays()
    {
        $enabled = [];
        foreach (self::WEEKDAY_BITS as $weekday => $bit) {
            if ($this->weekdays & $bit) {
                $enabled[] = [
                    'number' => $weekday,
                    'name_cn' => self::WEEKDAYS_CN[$weekday],
                    'bit' => $bit
                ];
            }
        }
        return $enabled;
    }

    /**
     * 获取格式化的调度信息
     */
    public function getFormattedSchedule()
    {
        $schedule = [];
        foreach (self::WEEKDAY_BITS as $weekday => $bit) {
            $schedule[$weekday] = [
                'enabled' => ($this->weekdays & $bit) > 0,
                'name_cn' => self::WEEKDAYS_CN[$weekday],
                'bit' => $bit
            ];
        }
        return $schedule;
    }

    /**
     * 批量设置星期启用状态
     */
    public function setWeekdays($weekdays)
    {
        if (is_array($weekdays)) {
            $this->weekdays = self::arrayToBitmask($weekdays);
        } else {
            $this->weekdays = $weekdays;
        }
        return $this->save();
    }

    /**
     * 获取启用天数统计
     */
    public function getEnabledDaysCount()
    {
        return count($this->bitmaskToArray());
    }

    /**
     * 获取位掩码的二进制表示（用于调试）
     */
    public function getBinaryRepresentation()
    {
        return sprintf('%07b', $this->weekdays);
    }
}
