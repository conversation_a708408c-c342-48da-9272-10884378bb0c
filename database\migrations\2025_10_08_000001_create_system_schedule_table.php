<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plc_system_schedule', function (Blueprint $table) {
            $table->id();
            $table->string('sn')->nullable()->comment('设备唯一编号');
            $table->boolean('monday')->default(true)->comment('周一是否启用');
            $table->boolean('tuesday')->default(true)->comment('周二是否启用');
            $table->boolean('wednesday')->default(true)->comment('周三是否启用');
            $table->boolean('thursday')->default(true)->comment('周四是否启用');
            $table->boolean('friday')->default(true)->comment('周五是否启用');
            $table->boolean('saturday')->default(false)->comment('周六是否启用');
            $table->boolean('sunday')->default(false)->comment('周日是否启用');
            $table->timestamps();

            $table->unique('sn', 'unique_sn');
            $table->index('sn', 'idx_sn');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plc_system_schedule');
    }
};
