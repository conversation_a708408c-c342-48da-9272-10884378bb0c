<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Api\Controller;
use App\Http\Controllers\Api\MqttController;
use App\Http\Controllers\Home\CodeController;
use App\Model\DeviceModels;
use App\Model\UserModels;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Log;

class DeviceController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */

    //设备列表
    public function index(Request $request)
    {

        $info = $request->all();
        $token = $request->header('token');

        $uid = $this->getPe($token);
        if (!$token) {
            return json_encode(['resutlt' => 1, 'message' => '请登录']);
        }
        if (!empty($info['page'])) {
            $page = $info['page'];
        } else {
            $page = 1;
        }

        $limit = empty($info['pagesize']) ? 10 : $info['pagesize'];
        // dd($uid);
        if ($uid['role'] == 0) {
            $user = DB::table('plc_device_user as t1')->select('t1.id', 't1.device', 't1.device_number', 't1.qiye_id', 't1.lat', 't1.lng', 't1.address', 't1.content', 't1.created_at', 't1.status')
                ->leftjoin('plc_user as t2', 't1.qiye_id', '=', 't2.id')
                ->where(['qiye_id' => $uid['id'], 't1.del' => null])
                ->where(function ($query) use ($request) {
                    $where = $request->input('sousuo');
                    // dd($where);
                    if (!empty($where)) {
                        $query->where('device', 'like', '%' . $where . '%');
                        $query->orwhere('device_number', 'like', '%' . $where . '%');
                    }
                })->paginate($limit);

            $user = objToArr($user);
            if (!$user) {
                return response()->json(['status' => 1, 'msg' => '未查询到相关信息！']);
            }
            foreach ($user['data'] as $key => $val) {
                $user['data'][$key]['error'] = 1;

                if (!empty(DB::table('plc_device_error')->where('u_id', $val['id'])->where('created_at', '>', time() - 30 * 86400)->where('has_sovle', 0)->get()->toArray())) {
                    $user['data'][$key]['error'] = 0;
                }
            }

            $count = DB::table('plc_device_details as a')->leftJoin('plc_device_user as b', 'a.sn', 'b.device_number')->where('qiye_id', $uid['id'])->where('b.del', null)->sum('zcq');
            $user['functionnumber'] = $count;
            return response()->json(['status' => 0, 'msg' => '查询成功！', 'page' => $user['current_page'], 'last_page' => $user['last_page'], 'data' => $user]);
        }
        $sousuo = $request->input('sousuo');

        $user = DB::table('plc_device_user as t1')->select('t1.id', 't1.device', 't1.qiye_id', 't1.device_number', 't1.address', 't1.lat', 't1.lng', 't1.content', 't1.created_at', 't2.enterprise', 't1.status')
            ->leftjoin('plc_user as t2', 't1.qiye_id', '=', 't2.id')
            ->where(['t1.del' => null])
            ->where(function ($query) use ($request) {
                $where = $request->input('sousuo');
                if (!empty($where)) {
                    $query->where('device', 'like', '%' . $where . '%');
                    $query->orwhere('device_number', 'like', '%' . $where . '%');
                    $query->orwhere('enterprise', 'like', '%' . $where . '%');
                }
            })->paginate($limit);

        $user = objToArr($user);

        if (!$user) {
            return response()->json(['status' => 1, 'msg' => '未查询到相关信息！']);
        }

        foreach ($user['data'] as $key => $val) {
            $user['data'][$key]['error'] = 1;

            if (!empty(DB::table('plc_device_error')->where('u_id', $val['id'])->where('created_at', '>', time() - 30 * 86400)->where('has_sovle', 0)->get()->toArray())) {
                $user['data'][$key]['error'] = 0;
            }
        }
        $count = DB::table('plc_device_details')->sum('zcq');
        $user['functionnumber'] = $count;

        return response()->json(['status' => 0, 'msg' => '查询成功！', 'page' => $user['current_page'], 'last_page' => $user['last_page'], 'data' => $user]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        //
        $token = $request->header('token');
        //        dd($token);
        $uid = $this->getPe($token);
        // dd($uid);

        if (!$token) {
            return json_encode(['resutlt' => 1, 'message' => '请登录']);
        }
        if ($uid['role'] == 0) {
            return json_encode(['resutlt' => 1, 'message' => '权限不足']);
        }
        $token = $request->header('token');
        //        dd($token);
        $uid = $this->getPe($token);
        if (!$token) {
            return json_encode(['resutlt' => 1, 'message' => '请登录']);
        }
        $user = UserModels::where('del', null)->select('id', 'enterprise')->get();
        //        dd($user);
        return response()->json(['status' => 0, 'msg' => '查询成功！', 'data' => $user]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    //设备新增
    public function store(Request $request)
    {
        //
        $token = $request->header('token');
        //        dd($token);
        $uid = $this->getPe($token);
        date_default_timezone_set('PRC'); //设置中国时区
        if (!$token) {
            return json_encode(['resutlt' => 1, 'message' => '请登录']);
        }
        if ($uid['role'] == 0) {
            return json_encode(['resutlt' => 1, 'message' => '权限不足']);
        }
        $user = $request->except('_token');
        //        dd($user);
        //进行验证
        //        $enterprise = UserModels::select('id','enterprise')->get();
        //        dd($enterprise);
        $rule = [
            'device' => 'required',
            'device_number' => 'required|unique:plc_device_user',
            'qiye_id' => 'required',
            'address' => 'required',
        ];

        $msg = [
            'device.required' => '设备名不能为空',
            'device_number.required' => '设备编号不能为空',
            'device_number.unique' => '设备编号已存在',
            'qiye_id.required' => '企业名称不能为空',
            'address.required' => '设备地址不能为空',
        ];
        $validator = Validator::make($user, $rule, $msg);
        if ($validator->fails()) {
            return response()->json(['status' => 1, 'msg' => '修改失败！', 'data' => $validator->errors()]);
        }
        //添加到数据库
        $qiye_id = DB::table('plc_user')->where(['id' => $user['qiye_id']])->get();
        // dd($qiye_id);
        $qiye_id = objToArr($qiye_id);
        if (!$qiye_id) {
            return response()->json(['status' => 0, 'msg' => '此企业不存在！']);
        }
        DB::table('plc_error_notice')->insertGetid(['sn' => $user['device_number']]);
        $res = DeviceModels::create([
            'device' => $user['device'],
            'device_number' => $user['device_number'],
            'qiye_id' => $user['qiye_id'],
            'address' => $user['address'],
            'lat' => $user['lat'],
            'lng' => $user['lng'],
            'content' => $user['content'],
        ]);
        //        dd($res);
        if ($res) {
            DB::table('plc_device_details')->insertGetid(['sn' => $user['device_number']]);
            DB::table('plc_general')->insertGetid(['sn' => $user['device_number']]);
            DB::table('plc_initial_install')->insertGetid(['sn' => $user['device_number']]);
            DB::table('plc_timing')->insertGetid(['sn' => $user['device_number']]);

            return response()->json(['status' => 0, 'msg' => '添加成功！']);
        } else {
            return response()->json(['status' => 1, 'msg' => '添加失败！']);
        }
        //        dd('$data');
        //        return $data;
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request)
    {
        //
        $token = $request->header('token');
        //        dd($token);
        $uid = $this->getPe($token);
        if (!$token) {
            return json_encode(['resutlt' => 1, 'message' => '请登录']);
        }
        if ($uid['role'] == 0) {
            return json_encode(['resutlt' => 1, 'message' => '权限不足']);
        }
        $id = $request->input('id');
        // dd($id);
        $data = DB::table('plc_device_user')->where(['id' => $id])->get();
        // dd($data);
        $user = UserModels::where('id', $id)->select('id', 'enterprise')->get();
        // dd($user);
        $data = objToArr($data);
        $data['qiye'] = objToArr($user);

        // dd($data);
        return response()->json(['status' => 0, 'msg' => '查询成功！', 'data' => $data]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        $token = $request->header('token');
        //        dd($token);
        $uid = $this->getPe($token);
        if (!$token) {
            return json_encode(['resutlt' => 1, 'message' => '请登录']);
        }
        if ($uid['role'] == 0) {
            return json_encode(['resutlt' => 1, 'message' => '权限不足']);
        }
        //获取提交过来的数据id
        $id = $request->input('id');
        //获取提交过来的数据
        $user = $request->except('_token');

        //数据验证
        $rule = [
            'device' => 'required',
            'device_number' => 'required',
            'qiye_id' => 'required',
            'address' => 'required',
        ];

        $msg = [
            'device.required' => '设备名不能为空',
            'device_number.required' => '设备编号不能为空',
            'qiye_id.required' => '企业名称不能为空',
            'address.required' => '设备地址不能为空',
        ];
        $validator = Validator::make($user, $rule, $msg);
        if ($validator->fails()) {
            return response()->json(['status' => 1, 'msg' => '修改失败！', 'data' => $validator->errors()]);
        }

        //添加到数据库
        $res = DeviceModels::where(['id' => $id])->update([
            'device' => $user['device'],
            'device_number' => $user['device_number'],
            'qiye_id' => $user['qiye_id'],
            'lat' => $user['lat'],
            'lng' => $user['lng'],
            'address' => $user['address'],
            'content' => $user['content'],
        ]);

        if ($res) {
            return response()->json(['status' => 0, 'msg' => '修改成功！']);
        } else {
            return response()->json(['status' => 1, 'msg' => '修改成功！']);
        }
        //        dd('$data');
        //        return $data;
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    //删除
    public function del(Request $request)
    {
        $token = $request->header('token');
        //        dd($token);
        $uid = $this->getPe($token);
        if (!$token) {
            return json_encode(['resutlt' => 1, 'message' => '请登录']);
        }
        if ($uid['role'] == 0) {
            return json_encode(['resutlt' => 1, 'message' => '权限不足']);
        }
        //        dd('13454');
        $input = $request->input('ids');
        // dd($input);
        foreach ($input as $k => $v) {
            $user[$k] = DeviceModels::where('id', $v)->get();
            $user[$k] = objToArr($user[$k]);
            $res = DeviceModels::where('id', $v)->delete();
            DB::table('plc_device_details')->where(['sn' => $user[$k][0]['device_number']])->delete();
            DB::table('plc_general')->where(['sn' => $user[$k][0]['device_number']])->delete();
            DB::table('plc_initial_install')->where(['sn' => $user[$k][0]['device_number']])->delete();
            DB::table('plc_timing')->where(['sn' => $user[$k][0]['device_number']])->delete();
            DB::table('plc_error_notice')->where(['sn' => $user[$k][0]['device_number']])->delete();
            DB::table('plc_date')->where(['sn' => $user[$k][0]['device_number']])->delete();
        }

        if ($res) {

            // DB::table('plc_error_notice')->where(['sn' =>$user['device_number']])->delete();
            return response()->json(['status' => 0, 'msg' => '删除成功！']);
        } else {
            return response()->json(['status' => 1, 'msg' => '删除失败！']);
        }
        //        return $data;
    }

    //设备详情
    public function details(Request $request)
    {
        $info = $request->all();
        $token = $request->header('token');
        //        dd($token);
        $uid = $this->getPe($token);
        if (!$token) {
            return json_encode(['resutlt' => 1, 'message' => '请登录']);
        }
        //设备详情首页
        $snnnnn = DB::table('plc_device_user')->where('id', $info['id'])->get();
        $snnnnn = objToArr($snnnnn);
        $snnnnns = DB::table('plc_device_details')->where('sn', $snnnnn[0]['device_number'])->get();
        $snnnnns = objToArr($snnnnns);

        $details = DB::table('plc_device_details as a')->select('b.device', 'b.address', 'a.sn', 'a.breset', 'a.pumpll', 'a.pumplls', 'a.bsurplus', 'a.bresets', 'a.bsurpluss', 'a.bstate', 'a.bstates', 'a.zcq', 'a.ms', 'a.jrcq', 'a.bxz', 'a.bcyjc')->leftjoin('plc_device_user as b', 'a.sn', '=', 'b.device_number')->where(['b.id' => $info['id']])->get();
        $details = objToArr($details);
        // dd($details);
        if (empty($details)) {
            // return 123;
            $lists = DB::table('plc_device_user')->where(['id' => $info['id']])->select('device', 'address', 'device_number as sn')->get();
            $details[0] = objToArr($lists[0]);
            $details[0]["breset"] = '';
            $details[0]["bsurplus"] = '';
            $details[0]["bresets"] = '';
            $details[0]["bsurpluss"] = '';
            $details[0]["bstate"] = '';
            $details[0]["bstates"] = '';
            $details[0]["zcq"] = '';
            $details[0]["ms"] = '';
            $details[0]["jrcq"] = '';
            $details[0]["bcyjc"] = '';
            $details[0]['pumpll'] = '';
            $details[0]['pumplls'] = '';
            $details[0]['bxz'] = '';
            DB::table('plc_device_details')->insertGetid(['sn' => $details[0]['sn']]);
        }
        //使用时间
        $usageTimes = DB::table('plc_date as a')->leftjoin('plc_device_user as b', 'a.sn', '=', 'b.device_number')->where(['b.id' => $info['id']])->select('a.bone', 'a.btwo', 'a.bthree', 'a.bfour', 'a.bfive', 'a.bsix', 'a.bseven', 'a.bones', 'a.btwos', 'a.bthrees', 'a.bfours', 'a.bfives', 'a.bsixs', 'a.bsevens')->get();
        $usageTimes = objToArr($usageTimes);
        // dd($usageTimes);
        if (empty($usageTimes)) {
            $sn = DB::table('plc_device_user')->where(['id' => $info['id']])->select('device_number')->get();
            $sn = objToArr($sn);
            // dd($sn);
            DB::table('plc_date')->insertGetid(['sn' => $sn[0]['device_number']]);
            $usageTimes = DB::table('plc_date as a')->leftjoin('plc_device_user as b', 'a.sn', '=', 'b.device_number')->where(['b.id' => $info['id']])->select('a.bone', 'a.btwo', 'a.bthree', 'a.bfour', 'a.bfive', 'a.bsix', 'a.bseven', 'a.bones', 'a.btwos', 'a.bthrees', 'a.bfours', 'a.bfives', 'a.bsixs', 'a.bsevens')->get();
            $usageTimes = objToArr($usageTimes);
            // $usageTimes = $usageTimes[0];
        }
        // dd($usageTimes);
        if (!empty($usageTimes)) {
            $usageTimes = $usageTimes[0];
            $usageTime[0]['day'] = 'bone';
            $usageTime[0]['value'] = $usageTimes['bone'];
            $usageTime[1]['day'] = 'btwo';
            $usageTime[1]['value'] = $usageTimes['btwo'];
            $usageTime[2]['day'] = 'bthree';
            $usageTime[2]['value'] = $usageTimes['bthree'];
            $usageTime[3]['day'] = 'bfour';
            $usageTime[3]['value'] = $usageTimes['bfour'];
            $usageTime[4]['day'] = 'bfive';
            $usageTime[4]['value'] = $usageTimes['bfive'];
            $usageTime[5]['day'] = 'bsix';
            $usageTime[5]['value'] = $usageTimes['bsix'];
            $usageTime[6]['day'] = 'bseven';
            $usageTime[6]['value'] = $usageTimes['bseven'];
            $usageTime[7]['day'] = 'bones';
            $usageTime[7]['value'] = $usageTimes['bones'];
            $usageTime[8]['day'] = 'btwos';
            $usageTime[8]['value'] = $usageTimes['btwos'];
            $usageTime[9]['day'] = 'bthrees';
            $usageTime[9]['value'] = $usageTimes['bthrees'];
            $usageTime[10]['day'] = 'bfours';
            $usageTime[10]['value'] = $usageTimes['bfours'];
            $usageTime[11]['day'] = 'bfives';
            $usageTime[11]['value'] = $usageTimes['bfives'];
            $usageTime[12]['day'] = 'bsixs';
            $usageTime[12]['value'] = $usageTimes['bsixs'];
            $usageTime[13]['day'] = 'bsevens';
            $usageTime[13]['value'] = $usageTimes['bsevens'];
        } else {
            $usageTime[0]['day'] = 'bone';
            $usageTime[0]['value'] = 0;
            $usageTime[1]['day'] = 'btwo';
            $usageTime[1]['value'] = 0;
            $usageTime[2]['day'] = 'bthree';
            $usageTime[2]['value'] = 0;
            $usageTime[3]['day'] = 'bfour';
            $usageTime[3]['value'] = 0;
            $usageTime[4]['day'] = 'bfive';
            $usageTime[4]['value'] = 0;
            $usageTime[5]['day'] = 'bsix';
            $usageTime[5]['value'] = 0;
            $usageTime[6]['day'] = 'bseven';
            $usageTime[6]['value'] = 0;
            $usageTime[7]['day'] = 'bones';
            $usageTime[7]['value'] = 0;
            $usageTime[8]['day'] = 'btwos';
            $usageTime[8]['value'] = 0;
            $usageTime[9]['day'] = 'bthrees';
            $usageTime[9]['value'] = 0;
            $usageTime[10]['day'] = 'bfours';
            $usageTime[10]['value'] = 0;
            $usageTime[11]['day'] = 'bfives';
            $usageTime[11]['value'] = 0;
            $usageTime[12]['day'] = 'bsixs';
            $usageTime[12]['value'] = 0;
            $usageTime[13]['day'] = 'bsevens';
            $usageTime[13]['value'] = 0;
        }
        // dd($usageTime);
        //定时启动设置
        $timing = DB::table('plc_timing as a')->leftjoin('plc_device_user as b', 'a.sn', '=', 'b.device_number')->where(['b.id' => $info['id']])->select('a.qdsA', 'a.qdsB', 'a.qdsC', 'a.qdsD', 'a.qdsE', 'a.qdsF', 'a.qdsG', 'a.qdsH', 'a.qdsI', 'a.qdsJ', 'a.qdsK', 'a.qdsL', 'a.qdsM', 'a.qdsN', 'a.qdsO', 'a.qdsP', 'a.qdsQ', 'a.qdsR', 'a.qdsS', 'a.qdsT', 'a.qdfA', 'a.qdfB', 'a.qdfC', 'a.qdfD', 'a.qdfE', 'a.qdfF', 'a.qdfG', 'a.qdfH', 'a.qdfI', 'a.qdfJ', 'a.qdfK', 'a.qdfL', 'a.qdfM', 'a.qdfN', 'a.qdfO', 'a.qdfP', 'a.qdfQ', 'a.qdfR', 'a.qdfS', 'a.qdfT', 'a.sn')->get();

        $timing = objToArr($timing);
        // dd($timing);
        if (empty($timing)) {
            $timings[0]['Sname'] = 'qdsA';
            $timings[1]['Sname'] = 'qdsB';
            $timings[2]['Sname'] = 'qdsC';
            $timings[3]['Sname'] = 'qdsD';
            $timings[4]['Sname'] = 'qdsE';
            $timings[5]['Sname'] = 'qdsF';
            $timings[6]['Sname'] = 'qdsG';
            $timings[7]['Sname'] = 'qdsH';
            $timings[7]['Sname'] = 'qdsH';
            $timings[9]['Sname'] = 'qdsJ';
            $timings[10]['Sname'] = 'qdsK';
            $timings[11]['Sname'] = 'qdsL';
            $timings[12]['Sname'] = 'qdsM';
            $timings[13]['Sname'] = 'qdsN';
            $timings[14]['Sname'] = 'qdsO';
            $timings[15]['Sname'] = 'qdsP';
            $timings[16]['Sname'] = 'qdsQ';
            $timings[17]['Sname'] = 'qdsR';
            $timings[18]['Sname'] = 'qdsS';
            $timings[19]['Sname'] = 'qdsT';
            $timings[0]['Fname'] = 'qdfA';
            $timings[1]['Fname'] = 'qdfB';
            $timings[2]['Fname'] = 'qdfC';
            $timings[3]['Fname'] = 'qdfD';
            $timings[4]['Fname'] = 'qdfE';
            $timings[5]['Fname'] = 'qdfF';
            $timings[6]['Fname'] = 'qdfG';
            $timings[7]['Fname'] = 'qdfH';
            $timings[8]['Fname'] = 'qdfI';
            $timings[9]['Fname'] = 'qdfJ';
            $timings[10]['Fname'] = 'qdfK';
            $timings[11]['Fname'] = 'qdfL';
            $timings[12]['Fname'] = 'qdfM';
            $timings[13]['Fname'] = 'qdfN';
            $timings[14]['Fname'] = 'qdfO';
            $timings[15]['Fname'] = 'qdfP';
            $timings[16]['Fname'] = 'qdfQ';
            $timings[17]['Fname'] = 'qdfR';
            $timings[18]['Fname'] = 'qdfS';
            $timings[19]['Fname'] = 'qdfT';
            $timings[0]['Stime'] = '';
            $timings[1]['Stime'] = '';
            $timings[2]['Stime'] = '';
            $timings[3]['Stime'] = '';
            $timings[4]['Stime'] = '';
            $timings[5]['Stime'] = '';
            $timings[6]['Stime'] = '';
            $timings[7]['Stime'] = '';
            $timings[7]['Stime'] = '';
            $timings[9]['Stime'] = '';
            $timings[10]['Stime'] = '';
            $timings[11]['Stime'] = '';
            $timings[12]['Stime'] = '';
            $timings[13]['Stime'] = '';
            $timings[14]['Stime'] = '';
            $timings[15]['Stime'] = '';
            $timings[16]['Stime'] = '';
            $timings[17]['Stime'] = '';
            $timings[18]['Stime'] = '';
            $timings[19]['Stime'] = '';
            $timings[0]['Ftime'] = '';
            $timings[1]['Ftime'] = '';
            $timings[2]['Ftime'] = '';
            $timings[3]['Ftime'] = '';
            $timings[4]['Ftime'] = '';
            $timings[5]['Ftime'] = '';
            $timings[6]['Ftime'] = '';
            $timings[7]['Ftime'] = '';
            $timings[8]['Ftime'] = '';
            $timings[9]['Ftime'] = '';
            $timings[10]['Ftime'] = '';
            $timings[11]['Ftime'] = '';
            $timings[12]['Ftime'] = '';
            $timings[13]['Ftime'] = '';
            $timings[14]['Ftime'] = '';
            $timings[15]['Ftime'] = '';
            $timings[16]['Ftime'] = '';
            $timings[17]['Ftime'] = '';
            $timings[18]['Ftime'] = '';
            $timings[19]['Ftime'] = '';

        } else {
            $timings[0]['Sname'] = 'qdsA';
            $timings[1]['Sname'] = 'qdsB';
            $timings[2]['Sname'] = 'qdsC';
            $timings[3]['Sname'] = 'qdsD';
            $timings[4]['Sname'] = 'qdsE';
            $timings[5]['Sname'] = 'qdsF';
            $timings[6]['Sname'] = 'qdsG';
            $timings[7]['Sname'] = 'qdsH';
            $timings[8]['Sname'] = 'qdsI';
            $timings[9]['Sname'] = 'qdsJ';
            $timings[10]['Sname'] = 'qdsK';
            $timings[11]['Sname'] = 'qdsL';
            $timings[12]['Sname'] = 'qdsM';
            $timings[13]['Sname'] = 'qdsN';
            $timings[14]['Sname'] = 'qdsO';
            $timings[15]['Sname'] = 'qdsP';
            $timings[16]['Sname'] = 'qdsQ';
            $timings[17]['Sname'] = 'qdsR';
            $timings[18]['Sname'] = 'qdsS';
            $timings[19]['Sname'] = 'qdsT';
            $timings[0]['Fname'] = 'qdfA';
            $timings[1]['Fname'] = 'qdfB';
            $timings[2]['Fname'] = 'qdfC';
            $timings[3]['Fname'] = 'qdfD';
            $timings[4]['Fname'] = 'qdfE';
            $timings[5]['Fname'] = 'qdfF';
            $timings[6]['Fname'] = 'qdfG';
            $timings[7]['Fname'] = 'qdfH';
            $timings[8]['Fname'] = 'qdfI';
            $timings[9]['Fname'] = 'qdfJ';
            $timings[10]['Fname'] = 'qdfK';
            $timings[11]['Fname'] = 'qdfL';
            $timings[12]['Fname'] = 'qdfM';
            $timings[13]['Fname'] = 'qdfN';
            $timings[14]['Fname'] = 'qdfO';
            $timings[15]['Fname'] = 'qdfP';
            $timings[16]['Fname'] = 'qdfQ';
            $timings[17]['Fname'] = 'qdfR';
            $timings[18]['Fname'] = 'qdfS';
            $timings[19]['Fname'] = 'qdfT';
            $timings[0]['Stime'] = $timing[0]['qdsA'];
            $timings[1]['Stime'] = $timing[0]['qdsB'];
            $timings[2]['Stime'] = $timing[0]['qdsC'];
            $timings[3]['Stime'] = $timing[0]['qdsD'];
            $timings[4]['Stime'] = $timing[0]['qdsE'];
            $timings[5]['Stime'] = $timing[0]['qdsF'];
            $timings[6]['Stime'] = $timing[0]['qdsG'];
            $timings[7]['Stime'] = $timing[0]['qdsH'];
            $timings[8]['Stime'] = $timing[0]['qdsI'];
            $timings[9]['Stime'] = $timing[0]['qdsJ'];
            $timings[10]['Stime'] = $timing[0]['qdsK'];
            $timings[11]['Stime'] = $timing[0]['qdsL'];
            $timings[12]['Stime'] = $timing[0]['qdsM'];
            $timings[13]['Stime'] = $timing[0]['qdsN'];
            $timings[14]['Stime'] = $timing[0]['qdsO'];
            $timings[15]['Stime'] = $timing[0]['qdsP'];
            $timings[16]['Stime'] = $timing[0]['qdsQ'];
            $timings[17]['Stime'] = $timing[0]['qdsR'];
            $timings[18]['Stime'] = $timing[0]['qdsS'];
            $timings[19]['Stime'] = $timing[0]['qdsT'];
            $timings[0]['Ftime'] = $timing[0]['qdfA'];
            $timings[1]['Ftime'] = $timing[0]['qdfB'];
            $timings[2]['Ftime'] = $timing[0]['qdfC'];
            $timings[3]['Ftime'] = $timing[0]['qdfD'];
            $timings[4]['Ftime'] = $timing[0]['qdfE'];
            $timings[5]['Ftime'] = $timing[0]['qdfF'];
            $timings[6]['Ftime'] = $timing[0]['qdfG'];
            $timings[7]['Ftime'] = $timing[0]['qdfH'];
            $timings[8]['Ftime'] = $timing[0]['qdfI'];
            $timings[9]['Ftime'] = $timing[0]['qdfJ'];
            $timings[10]['Ftime'] = $timing[0]['qdfK'];
            $timings[11]['Ftime'] = $timing[0]['qdfL'];
            $timings[12]['Ftime'] = $timing[0]['qdfM'];
            $timings[13]['Ftime'] = $timing[0]['qdfN'];
            $timings[14]['Ftime'] = $timing[0]['qdfO'];
            $timings[15]['Ftime'] = $timing[0]['qdfP'];
            $timings[16]['Ftime'] = $timing[0]['qdfQ'];
            $timings[17]['Ftime'] = $timing[0]['qdfR'];
            $timings[18]['Ftime'] = $timing[0]['qdfS'];
            $timings[19]['Ftime'] = $timing[0]['qdfT'];
        }
        // dd($timing);

        for ($x = 0; $x < 20; $x++) {
            // dd($timings);
            $timingss[$x] = $timings[$x];
        }
        // dd($timingss);

        //常用设置
        $general = DB::table('plc_general as a')->leftjoin('plc_device_user as b', 'a.sn', '=', 'b.device_number')->where(['b.id' => $info['id']])->select('a.zns', 'a.nswc', 'a.hxns', 'a.jyb', 'a.jybs', 'a.fan')->get();
        $general = objToArr($general);
        if (empty($general)) {
            $general[] = [
                'zns' => '', //总牛数
                'nswc' => '', //牛数误差
                'hxns' => '', //换洗牛数
                'jyb' => '', //泵1加药百分数
                'jybs' => '', //泵2加药百分数
                'fan' => '',
            ];
        }

        // dd($general);

        //报警设置
        $alarmSettings = DB::table('plc_error_notice as a')->leftjoin('plc_device_user as b', 'a.sn', '=', 'b.device_number')->where(['b.id' => $info['id']])->select('a.slgyq', 'a.waterPump', 'a.leveld', 'a.leveldz', 'a.csjwyxbj', 'a.weiyunxingsjsz', 'a.levelsd', 'a.levelsdz', 'a.yld', 'a.programError', 'a.berror', 'a.berrors')->get();
        $alarmSettings = objToArr($alarmSettings);
        if (empty($alarmSettings)) {
            $alarmSettings[] = [
                "slgyq" => '', //数量感应器
                'waterPump' => '',
                "leveld" => '', //泵1液位低
                "leveldz" => '', //泵1报警液位
                "levelsd" => '', //泵2液位低
                "levelsdz" => '', //泵2报警液位
                "yld" => '', //压力低
                "programError" => '', //程序执行异常
                "csjwyxbj" => '', //长时间未运行报警(1 报警 0 正常)
                "weiyunxingsjsz" => '', //未运行时间设置(0-32767H)
                'berror' => '',
                'berrora' => '',
            ];
        }
        // dd($alarmSettings);
        //初始设置
        if ($uid['role'] == 1) {
            $initial = DB::table('plc_initial_install as a')->leftjoin('plc_device_user as b', 'a.sn', '=', 'b.device_number')->where(['b.id' => $info['id']])->select('a.*')->get();
            $initial = objToArr($initial);
            if (empty($initial)) {
                $initial[] = [
                    'scf' => '', //首次冲洗时间分
                    'scm' => '', //首次冲洗时间秒
                    'zcf' => '', //正常冲洗时间分
                    'zcm' => '', //正常冲洗时间秒
                    'jsf' => '', //泵1加水时间分
                    'jsm' => '', //泵1加水时间秒
                    'jyf' => '', //加药时间分
                    'jym' => '', //加药时间秒
                    'qxs' => '', //清洗等待时间时
                    'qxf' => '', //清洗等待时间分
                    'gys' => '', //感应器等待时间(时)
                    'gyf' => '', //感应器等待时间(分)
                    'gym' => '', //感应器检测延时时间
                    "cxyskq" => '', //冲洗延时时间
                    "psysgb" => '', //排水延时关闭时间
                    "cxqgk" => '', //冲洗气缸开时间
                    "cxqgg" => '', //冲洗气缸关时间
                    "cd" => '', //长度(cm)
                    "kd" => '', //宽度(cm)
                    "tcgd" => '', //填充高度(cm)
                    "rl" => '', //容量
                    'jsf2' => '', //泵2加水时间分
                    'jsm2' => '', //泵2加水时间秒
                ];
            }
        } else {
            $initial = DB::table('plc_initial_install as a')->leftjoin('plc_device_user as b', 'a.sn', '=', 'b.device_number')->where(['b.id' => $info['id']])->select('a.count_start_h','a.count_start_m')->get();
            $initial = objToArr($initial);
        }

        $date = [];
        $format = 'Y-m-d';
        for ($i = 0; $i < 1; $i++) {
            $strtotime = strtotime('+' . $i . ' days');
            $date[$i]['date'] = date($format, $strtotime);
            $date[$i]['week'] = "星期" . mb_substr("日一二三四五六", date("w", $strtotime), 1, "utf-8");
        }
        return json_encode(['resutlt' => 0, 'message' => '成功', 'details' => $details, 'usageTime' => $usageTime, 'timing' => $timingss, 'alarmSettings' => $alarmSettings, 'initial' => $initial, 'general' => $general, 'date' => $date]);
    }

    //泵余量
    public function deviceEdit(Request $request)
    {
        $info = $request->all();
        $info = $info['data'];
        $clientid = $info['sn'];
        $token = $request->header('token');
        //        dd($token);
        $uid = $this->getPe($token);
        // dd($info);
        if (!$token) {
            return json_encode(['resutlt' => 1, 'message' => '请登录']);
        }

        //泵1重置
        if ($info['type'] == 1) {
            $pumpll = DB::table('plc_device_details')->where('sn', $info['sn'])->get();
            $pumpll = objToArr($pumpll);
            if (!empty($pumpll)) {
                $info['pumplls'] = $pumpll[0]['pumpll'];
            }
            // $info['pumplls'] = 999;
            // $data[0]['clientid'] = $info['sn'];
            // $data[0]['variable'] = $info['pumplls'];
            // $data[0]['weizhi'] = 100;
            // $data[1]['clientid'] = $info['sn'];
            // $data[1]['variable'] = $info['pumplls'];
            // $data[1]['weizhi'] = 101;
            DB::table('plc_device_details')->where('sn', $info['sn'])->update(['breset' => $info['pumplls'], 'bsurplus' => $info['pumplls']]);
            $u_id = DB::table('plc_device_user')->where('device_number', $info['sn'])->where('del', null)->value('id');
            // $the = new MqttController;
            DB::table('plc_device_error')->where(['u_id' => $u_id, 'c_id' => 2, 'has_sovle' => 0])->update(['has_sovle' => 1]);
            // return $the->fasong_s($data,2);
            return json_encode(['resutlt' => 0, 'message' => '成功']);

        }
        //泵1流量设定
        elseif ($info['type'] == 2) {
            $pumpll = DB::table('plc_device_details')->where('sn', $info['sn'])->get();
            $pumpll = objToArr($pumpll);
            DB::table('plc_device_details')->where('sn', $info['sn'])->update(['pumpll' => $info['pumpll'], 'bsurplus' => $info['pumpll']]);
            $u_id = DB::table('plc_device_user')->where('device_number', $info['sn'])->where('del', null)->value('id');
            DB::table('plc_device_error')->where(['u_id' => $u_id, 'c_id' => 2, 'has_sovle' => 0])->update(['has_sovle' => 1]);
            return json_encode(['resutlt' => 0, 'message' => '成功']);
        }
        //泵2重置
        elseif ($info['type'] == 3) {
            $pumpll = DB::table('plc_device_details')->where('sn', $info['sn'])->get();
            $pumpll = objToArr($pumpll);
            if (!empty($pumpll)) {
                $info['pumplls'] = $pumpll[0]['pumplls'];
            }

            DB::table('plc_device_details')->where('sn', $info['sn'])->update(['bresets' => $info['pumplls'], 'bsurpluss' => $info['pumplls']]);
            // $the = new MqttController;
            $u_id = DB::table('plc_device_user')->where('device_number', $info['sn'])->where('del', null)->value('id');
            DB::table('plc_device_error')->where(['u_id' => $u_id, 'c_id' => 3, 'has_sovle' => 0])->update(['has_sovle' => 1]);
            // return $the->fasong_s($data,2);
            return json_encode(['resutlt' => 0, 'message' => '成功']);
        }
        //泵2流量设定
        elseif ($info['type'] == 4) {
            $pumpll = DB::table('plc_device_details')->where('sn', $info['sn'])->get();
            $pumpll = objToArr($pumpll);

            DB::table('plc_device_details')->where('sn', $info['sn'])->update(['pumplls' => $info['pumplls'], 'bsurpluss' => $info['pumplls']]);
            $u_id = DB::table('plc_device_user')->where('device_number', $info['sn'])->where('del', null)->value('id');
            DB::table('plc_device_error')->where(['u_id' => $u_id, 'c_id' => 3, 'has_sovle' => 0])->update(['has_sovle' => 1]);
            return json_encode(['resutlt' => 0, 'message' => '成功']);
        } else {
            return json_encode(['resutlt' => 1, 'message' => '缺少参数']);
        }
    }

    //报警设置
    public function setuperror(Request $request)
    {
        $info = $request->all();
        $token = $request->header('token');

        $info = $info['data'];
        $uid = $this->getPe($token);

        if (!$token) {
            return json_encode(['resutlt' => 1, 'message' => '请登录']);
        }
        //数量感应器
        elseif (!empty($info['slgyq'])) {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['slgyq'] = $info['slgyq'];
            $res = DB::table('plc_error_notice')->where(['sn' => $info['sn']])->update(['slgyq' => $data[0]['slgyq']]);
            if ($res) {
                return json_encode(['resutlt' => 0, 'message' => '成功']);
            } else {
                return json_encode(['resutlt' => 1, 'message' => '失败']);
            }
        }
        //水泵(0 停止1运行 2故障 )
        elseif (!empty($info['waterPump'])) {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['waterPump'] = $info['waterPump'];
            $res = DB::table('plc_error_notice')->where(['sn' => $info['sn']])->update(['waterPump' => $data[0]['waterPump']]);
            if ($res) {
                return json_encode(['resutlt' => 0, 'message' => '成功']);
            } else {
                return json_encode(['resutlt' => 1, 'message' => '失败']);
            }
        }
        //泵1液位低
        elseif (!empty($info['leveld'])) {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['leveld'] = $info['leveld'];
            $res = DB::table('plc_error_notice')->where(['sn' => $info['sn']])->update(['leveld' => $data[0]['leveld']]);
            if ($res) {
                return json_encode(['resutlt' => 0, 'message' => '成功']);
            } else {
                return json_encode(['resutlt' => 1, 'message' => '失败']);
            }
        }
        //泵1报警液位
        elseif (!empty($info['leveldz'])) {
            $device = DB::table('plc_device_details')->where('sn', $info['sn'])->get();
            $device = objToArr($device);
            if (!empty($device)) {
                if ($device[0]['pumpll'] < $info['leveldz']) {
                    return json_encode(['resutlt' => 1, 'message' => '泵1报警液位低于泵1设定量']);
                }
            }
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['leveldz'];
            $data[0]['weizhi'] = 109;
            $res = DB::table('plc_error_notice')->where(['sn' => $info['sn']])->update(['leveldz' => $info['leveldz']]);
            DB::table('plc_device_error')->where(['u_id' => $uid, 'c_id' => 2, 'has_sovle' => 0])->update(['has_sovle' => 1]);
            $the = new MqttController;
            return $the->fasong($data);
        }
        //泵2液位低
        elseif (!empty($info['levelsd'])) {

            $data[0]['clientid'] = $info['sn'];
            $data[0]['levelsd'] = $info['levelsd'];
            // $data[0]['weizhi'] = 220;
            // $the = new MqttController;
            // return $the->fasong_s($data,2);
            $res = DB::table('plc_error_notice')->where(['sn' => $info['sn']])->update(['levelsd' => $info['levelsd']]);
            if ($res) {
                return json_encode(['resutlt' => 0, 'message' => '成功']);
            } else {
                return json_encode(['resutlt' => 1, 'message' => '失败']);
            }
        }
        //泵2报警液位
        elseif (!empty($info['levelsdz'])) {
            $device = DB::table('plc_device_details')->where('sn', $info['sn'])->get();
            $device = objToArr($device);
            if (!empty($device)) {
                if ($device[0]['pumplls'] < $info['levelsdz']) {
                    return json_encode(['resutlt' => 1, 'message' => '泵2报警液位低于泵2设定量']);
                }
            }
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['levelsdz'];
            $data[0]['weizhi'] = 111;
            $res = DB::table('plc_error_notice')->where(['sn' => $info['sn']])->update(['levelsdz' => $info['levelsdz']]);
            DB::table('plc_device_error')->where(['u_id' => $uid, 'c_id' => 3, 'has_sovle' => 0])->update(['has_sovle' => 1]);
            $the = new MqttController;
            return $the->fasong($data);
        }
        //程序执行异常
        elseif (!empty($info['programError'])) {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['programError'] = $info['programError'];
            // $data[0]['weizhi'] = 226;
            // $the = new MqttController;
            // return $the->fasong_s($data,2);
            $res = DB::table('plc_error_notice')->where(['sn' => $info['sn']])->update(['programError' => $info['programError']]);
            if ($res) {
                return json_encode(['resutlt' => 0, 'message' => '成功']);
            } else {
                return json_encode(['resutlt' => 1, 'message' => '失败']);
            }
        }
        //长时间未运行
        elseif (!empty($info['csjwyxbj'])) {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['csjwyxbj'] = $info['csjwyxbj'];
            // $data[0]['weizhi'] = 214;
            // $the = new MqttController;
            // return $the->fasong_s($data,2);
            $res = DB::table('plc_error_notice')->where(['sn' => $info['sn']])->update(['csjwyxbj' => $info['csjwyxbj']]);
            if ($res) {
                return json_encode(['resutlt' => 0, 'message' => '成功']);
            } else {
                return json_encode(['resutlt' => 1, 'message' => '失败']);
            }
        }
        //压力低
        elseif (!empty($info['yld'])) {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['yld'] = $info['yld'];
            // $data[0]['weizhi'] = 224;
            // $the = new MqttController;
            // return $the->fasong_s($data,2);
            $res = DB::table('plc_error_notice')->where(['sn' => $info['sn']])->update(['yld' => $data[0]['yld']]);
            if ($res) {
                return json_encode(['resutlt' => 0, 'message' => '成功']);
            } else {
                return json_encode(['resutlt' => 1, 'message' => '失败']);
            }
        }
        //未运行时间设置(0-32767H)
        elseif (!empty($info['weiyunxingsjsz'])) {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['weiyunxingsjsz'];
            $data[0]['weizhi'] = 116;
            DB::table('plc_error_notice')->where(['sn' => $info['sn']])->update(['weiyunxingsjsz' => $info['weiyunxingsjsz']]);
            $the = new MqttController;
            return $the->fasong($data);
        } elseif (!empty($info['berror'])) {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['berror'] = $info['berror'];
            $res = DB::table('plc_error_notice')->where(['sn' => $info['sn']])->update(['berror' => $data[0]['berror']]);
            if ($res) {
                return json_encode(['resutlt' => 0, 'message' => '成功']);
            } else {
                return json_encode(['resutlt' => 1, 'message' => '失败']);
            }
        } elseif (!empty($info['berrors'])) {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['berrors'] = $info['berrors'];
            $res = DB::table('plc_error_notice')->where(['sn' => $info['sn']])->update(['berrors' => $data[0]['berrors']]);
            if ($res) {
                return json_encode(['resutlt' => 0, 'message' => '成功']);
            } else {
                return json_encode(['resutlt' => 1, 'message' => '失败']);
            }
        } else {
            return json_encode(['resutlt' => 1, 'message' => '缺少参数']);
        }
    }

    //使用时间
    public function usagetime(Request $request)
    {
        $info = $request->all();
        // $clientid = $request->input('sn');
        $token = $request->header('token');
        //        dd($token);
        $uid = $this->getPe($token);
        $info = $info['data'];
        if (!$token) {
            return json_encode(['resutlt' => 1, 'message' => '请登录']);
        }
        // dd($info);
        // $info = $info['data'];
        //泵1星期一使用
        if ($info['day'] == 'bone') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['value'];
            $data[0]['weizhi'] = 39;
            DB::table('plc_date')->where('sn', $info['sn'])->update(['bone' => $info['value']]);
            $the = new MqttController;
            return $the->fasong($data);
        }
        //泵2星期一使用
        elseif ($info['day'] == 'bones') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['value'];
            $data[0]['weizhi'] = 46;
            DB::table('plc_date')->where('sn', $info['sn'])->update(['bones' => $info['value']]);
            $the = new MqttController;
            return $the->fasong($data);
        }
        //泵1星期二使用
        elseif ($info['day'] == 'btwo') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['value'];
            $data[0]['weizhi'] = 40;
            DB::table('plc_date')->where('sn', $info['sn'])->update(['btwo' => $info['value']]);
            $the = new MqttController;
            return $the->fasong($data);
        }
        //泵2星期二使用
        elseif ($info['day'] == 'btwos') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['value'];
            $data[0]['weizhi'] = 47;
            DB::table('plc_date')->where('sn', $info['sn'])->update(['btwos' => $info['value']]);
            $the = new MqttController;
            return $the->fasong($data);
        }
        //泵1星期三使用
        elseif ($info['day'] == 'bthree') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['value'];
            $data[0]['weizhi'] = 41;
            DB::table('plc_date')->where('sn', $info['sn'])->update(['bthree' => $info['value']]);
            $the = new MqttController;
            return $the->fasong($data);
        }
        //泵2星期三使用
        elseif ($info['day'] == 'bthrees') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['value'];
            $data[0]['weizhi'] = 48;
            DB::table('plc_date')->where('sn', $info['sn'])->update(['bthrees' => $info['value']]);
            $the = new MqttController;
            return $the->fasong($data);
        }
        //泵1星期四使用
        elseif ($info['day'] == 'bfour') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['value'];
            $data[0]['weizhi'] = 42;
            DB::table('plc_date')->where('sn', $info['sn'])->update(['bfour' => $info['value']]);
            $the = new MqttController;
            return $the->fasong($data);
        }
        //泵2星期四使用
        elseif ($info['day'] == 'bfours') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['value'];
            $data[0]['weizhi'] = 49;
            DB::table('plc_date')->where('sn', $info['sn'])->update(['bfours' => $info['value']]);
            $the = new MqttController;
            return $the->fasong($data);
        }
        //泵1星期五使用
        elseif ($info['day'] == 'bfive') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['value'];
            $data[0]['weizhi'] = 43;
            DB::table('plc_date')->where('sn', $info['sn'])->update(['bfive' => $info['value']]);
            $the = new MqttController;
            return $the->fasong($data);
        }
        //泵2星期五使用
        elseif ($info['day'] == 'bfives') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['value'];
            $data[0]['weizhi'] = 50;
            DB::table('plc_date')->where('sn', $info['sn'])->update(['bfives' => $info['value']]);
            $the = new MqttController;
            return $the->fasong($data);
        }
        //泵1星期六使用
        elseif ($info['day'] == 'bsix') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['value'];
            $data[0]['weizhi'] = 44;
            DB::table('plc_date')->where('sn', $info['sn'])->update(['bsix' => $info['value']]);
            $the = new MqttController;
            return $the->fasong($data);
        }
        //泵2星期六使用
        elseif ($info['day'] == 'bsixs') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['value'];
            $data[0]['weizhi'] = 51;
            DB::table('plc_date')->where('sn', $info['sn'])->update(['bsixs' => $info['value']]);
            $the = new MqttController;
            return $the->fasong($data);
        }
        //泵1星期日使用
        elseif ($info['day'] == 'bseven') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['value'];
            $data[0]['weizhi'] = 45;
            DB::table('plc_date')->where('sn', $info['sn'])->update(['bseven' => $info['value']]);
            $the = new MqttController;
            return $the->fasong($data);
        }
        //泵2星期日使用
        elseif ($info['day'] == 'bsevens') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['value'];
            $data[0]['weizhi'] = 52;
            DB::table('plc_date')->where('sn', $info['sn'])->update(['bsevens' => $info['value']]);
            $the = new MqttController;
            return $the->fasong($data);
        } else {
            return json_encode(['resutlt' => 1, 'message' => '缺少参数']);
        }
    }

    //定时启动设置
    public function dingshi(Request $request)
    {
        $info = $request->all();
        $clientid = $request->input('sn');
        $token = $request->header('token');
        //        dd($token);
        $uid = $this->getPe($token);
        // dd($info);
        $info = $info['data'];
        // dd($info);
        $sn = $info['sn'];
        if (!$token) {
            return json_encode(['resutlt' => 1, 'message' => '请登录']);
        }
        //定时启动设置
        $snname = DB::table('plc_timing')->where('sn', $sn)->get();
        $snname = objToArr($snname);
        if (empty($snname)) {
            DB::table('plc_timing')->insertGetid(['sn' => $sn]);
        }
        if ($info['Fname'] == '1') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['Stime'];
            $data[0]['weizhi'] = 53;
            $data[1]['clientid'] = $info['sn'];
            $data[1]['variable'] = $info['Ftime'];
            $data[1]['weizhi'] = 54;
            DB::table('plc_timing')->where('sn', $sn)->update(['qdsA' => $info['Stime'], 'qdfA' => $info['Ftime']]);

            $the = new MqttController;

            return $the->fasong_s($data, 2);
        } elseif ($info['Fname'] == '2') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['Stime'];
            $data[0]['weizhi'] = 55;
            $data[1]['clientid'] = $info['sn'];
            $data[1]['variable'] = $info['Ftime'];
            $data[1]['weizhi'] = 56;
            DB::table('plc_timing')->where('sn', $sn)->update(['qdsB' => $info['Stime'], 'qdfB' => $info['Ftime']]);
            $the = new MqttController;
            return $the->fasong_s($data, 2);
        } elseif ($info['Fname'] == '3') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['Stime'];
            $data[0]['weizhi'] = 57;
            $data[1]['clientid'] = $info['sn'];
            $data[1]['variable'] = $info['Ftime'];
            $data[1]['weizhi'] = 58;
            DB::table('plc_timing')->where('sn', $sn)->update(['qdsC' => $info['Stime'], 'qdfC' => $info['Ftime']]);
            $the = new MqttController;
            return $the->fasong_s($data, 2);
        } elseif ($info['Fname'] == '4') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['Stime'];
            $data[0]['weizhi'] = 59;
            $data[1]['clientid'] = $info['sn'];
            $data[1]['variable'] = $info['Ftime'];
            $data[1]['weizhi'] = 60;
            DB::table('plc_timing')->where('sn', $sn)->update(['qdsD' => $info['Stime'], 'qdfD' => $info['Ftime']]);
            $the = new MqttController;
            return $the->fasong_s($data, 2);
        } elseif ($info['Fname'] == '5') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['Stime'];
            $data[0]['weizhi'] = 61;
            $data[1]['clientid'] = $info['sn'];
            $data[1]['variable'] = $info['Ftime'];
            $data[1]['weizhi'] = 62;
            DB::table('plc_timing')->where('sn', $sn)->update(['qdsE' => $info['Stime'], 'qdfE' => $info['Ftime']]);
            $the = new MqttController;
            return $the->fasong_s($data, 2);
        } elseif ($info['Fname'] == '6') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['Stime'];
            $data[0]['weizhi'] = 63;
            $data[1]['clientid'] = $info['sn'];
            $data[1]['variable'] = $info['Ftime'];
            $data[1]['weizhi'] = 64;
            DB::table('plc_timing')->where('sn', $sn)->update(['qdsF' => $info['Stime'], 'qdfF' => $info['Ftime']]);
            $the = new MqttController;
            return $the->fasong_s($data, 2);
        } elseif ($info['Fname'] == '7') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['Stime'];
            $data[0]['weizhi'] = 65;
            $data[1]['clientid'] = $info['sn'];
            $data[1]['variable'] = $info['Ftime'];
            $data[1]['weizhi'] = 66;
            DB::table('plc_timing')->where('sn', $sn)->update(['qdsG' => $info['Stime'], 'qdfG' => $info['Ftime']]);
            $the = new MqttController;
            return $the->fasong_s($data, 2);
        } elseif ($info['Fname'] == '8') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['Stime'];
            $data[0]['weizhi'] = 67;
            $data[1]['clientid'] = $info['sn'];
            $data[1]['variable'] = $info['Ftime'];
            $data[1]['weizhi'] = 68;
            DB::table('plc_timing')->where('sn', $sn)->update(['qdsH' => $info['Stime'], 'qdfH' => $info['Ftime']]);
            $the = new MqttController;
            return $the->fasong_s($data, 2);
        } elseif ($info['Fname'] == '9') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['Stime'];
            $data[0]['weizhi'] = 69;
            $data[1]['clientid'] = $info['sn'];
            $data[1]['variable'] = $info['Ftime'];
            $data[1]['weizhi'] = 70;
            DB::table('plc_timing')->where('sn', $sn)->update(['qdsI' => $info['Stime'], 'qdfI' => $info['Ftime']]);
            $the = new MqttController;
            return $the->fasong_s($data, 2);
        } elseif ($info['Fname'] == '10') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['Stime'];
            $data[0]['weizhi'] = 71;
            $data[1]['clientid'] = $info['sn'];
            $data[1]['variable'] = $info['Ftime'];
            $data[1]['weizhi'] = 72;
            DB::table('plc_timing')->where('sn', $sn)->update(['qdsJ' => $info['Stime'], 'qdfJ' => $info['Ftime']]);
            $the = new MqttController;
            return $the->fasong_s($data, 2);
        } elseif ($info['Fname'] == '11') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['Stime'];
            $data[0]['weizhi'] = 73;
            $data[1]['clientid'] = $info['sn'];
            $data[1]['variable'] = $info['Ftime'];
            $data[1]['weizhi'] = 74;
            DB::table('plc_timing')->where('sn', $sn)->update(['qdsK' => $info['Stime'], 'qdfK' => $info['Ftime']]);
            $the = new MqttController;
            return $the->fasong_s($data, 2);
        } elseif ($info['Fname'] == '12') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['Stime'];
            $data[0]['weizhi'] = 75;
            $data[1]['clientid'] = $info['sn'];
            $data[1]['variable'] = $info['Ftime'];
            $data[1]['weizhi'] = 76;
            DB::table('plc_timing')->where('sn', $sn)->update(['qdsL' => $info['Stime'], 'qdfL' => $info['Ftime']]);
            $the = new MqttController;
            return $the->fasong_s($data, 2);
        } elseif ($info['Fname'] == '13') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['Stime'];
            $data[0]['weizhi'] = 77;
            $data[1]['clientid'] = $info['sn'];
            $data[1]['variable'] = $info['Ftime'];
            $data[1]['weizhi'] = 78;
            DB::table('plc_timing')->where('sn', $sn)->update(['qdsM' => $info['Stime'], 'qdfM' => $info['Ftime']]);
            $the = new MqttController;
            return $the->fasong_s($data, 2);
        } elseif ($info['Fname'] == '14') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['Stime'];
            $data[0]['weizhi'] = 79;
            $data[1]['clientid'] = $info['sn'];
            $data[1]['variable'] = $info['Ftime'];
            $data[1]['weizhi'] = 80;
            DB::table('plc_timing')->where('sn', $sn)->update(['qdsN' => $info['Stime'], 'qdfN' => $info['Ftime']]);
            $the = new MqttController;
            return $the->fasong_s($data, 2);
        } elseif ($info['Fname'] == '15') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['Stime'];
            $data[0]['weizhi'] = 81;
            $data[1]['clientid'] = $info['sn'];
            $data[1]['variable'] = $info['Ftime'];
            $data[1]['weizhi'] = 82;
            DB::table('plc_timing')->where('sn', $sn)->update(['qdsO' => $info['Stime'], 'qdfO' => $info['Ftime']]);
            $the = new MqttController;
            return $the->fasong_s($data, 2);
        } elseif ($info['Fname'] == '16') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['Stime'];
            $data[0]['weizhi'] = 83;
            $data[1]['clientid'] = $info['sn'];
            $data[1]['variable'] = $info['Ftime'];
            $data[1]['weizhi'] = 84;
            DB::table('plc_timing')->where('sn', $sn)->update(['qdsP' => $info['Stime'], 'qdfP' => $info['Ftime']]);
            $the = new MqttController;
            return $the->fasong_s($data, 2);
        } elseif ($info['Fname'] == '17') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['Stime'];
            $data[0]['weizhi'] = 85;
            $data[1]['clientid'] = $info['sn'];
            $data[1]['variable'] = $info['Ftime'];
            $data[1]['weizhi'] = 86;
            DB::table('plc_timing')->where('sn', $sn)->update(['qdsQ' => $info['Stime'], 'qdfQ' => $info['Ftime']]);
            $the = new MqttController;
            return $the->fasong_s($data, 2);
        } elseif ($info['Fname'] == '18') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['Stime'];
            $data[0]['weizhi'] = 87;
            $data[1]['clientid'] = $info['sn'];
            $data[1]['variable'] = $info['Ftime'];
            $data[1]['weizhi'] = 88;
            DB::table('plc_timing')->where('sn', $sn)->update(['qdsR' => $info['Stime'], 'qdfR' => $info['Ftime']]);
            $the = new MqttController;
            return $the->fasong_s($data, 2);
        } elseif ($info['Fname'] == '19') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['Stime'];
            $data[0]['weizhi'] = 89;
            $data[1]['clientid'] = $info['sn'];
            $data[1]['variable'] = $info['Ftime'];
            $data[1]['weizhi'] = 90;
            DB::table('plc_timing')->where('sn', $sn)->update(['qdsS' => $info['Stime'], 'qdfS' => $info['Ftime']]);
            $the = new MqttController;
            return $the->fasong_s($data, 2);
        } elseif ($info['Fname'] == '20') {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['Stime'];
            $data[0]['weizhi'] = 91;
            $data[1]['clientid'] = $info['sn'];
            $data[1]['variable'] = $info['Ftime'];
            $data[1]['weizhi'] = 92;
            DB::table('plc_timing')->where('sn', $sn)->update(['qdsT' => $info['Stime'], 'qdfT' => $info['Ftime']]);
            $the = new MqttController;
            return $the->fasong_s($data, 2);
        } else {
            return json_encode(['resutlt' => 1, 'message' => '缺少参数']);
        }
    }

    //常用设置
    public function changyong(Request $request)
    {
        $info = $request->all();
        $clientid = $request->input('sn');
        $token = $request->header('token');

        $info = $info['data'];
        if (!$token) {
            return json_encode(['resutlt' => 1, 'message' => '请登录']);
        }
        $snname = DB::table('plc_general')->where('sn', $info['sn'])->get();
        $snname = objToArr($snname);
        if (empty($snname)) {
            DB::table('plc_general')->insertGetid(['sn' => $info['sn']]);
        }
        //常用设置
        if (!empty($info)) {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['zns'];
            $data[0]['weizhi'] = 20;
            $data[1]['clientid'] = $info['sn'];
            $data[1]['variable'] = $info['nswc'];
            $data[1]['weizhi'] = 22;
            $data[2]['clientid'] = $info['sn'];
            $data[2]['variable'] = $info['hxns'];
            $data[2]['weizhi'] = 23;
            $data[3]['clientid'] = $info['sn'];
            $data[3]['variable'] = $info['fan'];
            $data[3]['weizhi'] = 103;
            DB::table('plc_general')->where('sn', $info['sn'])->update(['zns' => $info['zns'], 'nswc' => $info['nswc'], 'hxns' => $info['hxns'], 'jyb' => $info['jyb'], 'jybs' => $info['jybs'], 'fan' => $info['fan']]);
            $the = new MqttController;
            $the->fasong($data);

            //泵1百分比修改后修改泵1加药时间
            $deivceInfo = DB::table('plc_initial_install')->where('sn', $info['sn'])->first();
            $rl = ($deivceInfo->cd * $deivceInfo->kd * $deivceInfo->tcgd) / 1000;
            $addtime = ($rl * $info['jybs'] * 0.01) / 0.24 / 60;
            $addtime = explode('.', $addtime);
            $min = $addtime[0];
            $sec = $addtime[1] ? intval(('0.'.$addtime[1])*60) : 0;

            $data_[0]['clientid'] = $info['sn'];
            $data_[0]['variable'] = $min;
            $data_[0]['weizhi'] = 6;
            $data_[1]['clientid'] = $info['sn'];
            $data_[1]['variable'] = $sec;
            $data_[1]['weizhi'] = 7;
            $the->fasong_s($data_, 2);

            return json_encode(['resutlt' => 0, 'message' => '成功']);
        } else {
            return json_encode(['resutlt' => 1, 'message' => '缺少参数']);
        }
    }

    //初始设置
    public function chushi(Request $request)
    {
        $info = $request->all();
        // $clientid = $request->input('sn');
        $token = $request->header('token');
        //        dd($token);
        $info = $info['data'];
        $uid = $this->getPe($token);
        $snname = DB::table('plc_initial_install')->where('sn', $info['sn'])->get();
        $snname = objToArr($snname);

        if (empty($snname)) {
            DB::table('plc_initial_install')->insertGetid(['sn' => $info['sn']]);
        }
        if (!$token) {
            return json_encode(['resutlt' => 1, 'message' => '请登录']);
        }
        //长度
        elseif (!empty($info['cd'])) {

            $plc_initial_install = DB::table('plc_initial_install')->where('sn', $info['sn'])->first();
            $plc_initial_install = objToArr($plc_initial_install);
            $rl = ($info['cd'] * $plc_initial_install['kd'] * $plc_initial_install['tcgd']) / 1000;
            DB::table('plc_initial_install')->where('sn', $info['sn'])->update(['rl' => $rl, 'cd' => $info['cd']]);
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['cd'];
            $data[0]['weizhi'] = 17;
            $the = new MqttController;
            $the->fasong($data);
            return json_encode(['resutlt' => 0, 'message' => '成功']);  
        }
        //宽度
        elseif (!empty($info['kd'])) {
            $plc_initial_install = DB::table('plc_initial_install')->where('sn', $info['sn'])->select('cd', 'tcgd')->get();
            $plc_initial_install = objToArr($plc_initial_install);
            $rl = ($info['kd'] * $plc_initial_install[0]['cd'] * $plc_initial_install[0]['tcgd']) / 1000;
            DB::table('plc_initial_install')->where('sn', $info['sn'])->update(['rl' => $rl, 'kd' => $info['kd']]);

            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['kd'];
            $data[0]['weizhi'] = 18;
            $the = new MqttController;
            $the->fasong($data);
            return json_encode(['resutlt' => 0, 'message' => '成功']);  
        }
        //填充高度
        elseif (!empty($info['tcgd'])) {
            $plc_initial_install = DB::table('plc_initial_install')->where('sn', $info['sn'])->select('cd', 'kd')->get();
            $plc_initial_install = objToArr($plc_initial_install);
            $rl = ($info['tcgd'] * $plc_initial_install[0]['cd'] * $plc_initial_install[0]['kd']) / 1000;
            DB::table('plc_initial_install')->where('sn', $info['sn'])->update(['rl' => $rl, 'tcgd' => $info['tcgd']]);
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['tcgd'];
            $data[0]['weizhi'] = 19;
            $the = new MqttController;
            $the->fasong($data);
            return json_encode(['resutlt' => 0, 'message' => '成功']);  
        }
        //首次冲洗时间分和秒
        elseif ($info['type'] == 1) {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['scf'];
            $data[0]['weizhi'] = 0;
            $data[1]['clientid'] = $info['sn'];
            $data[1]['variable'] = $info['scm'];
            $data[1]['weizhi'] = 1;
            $res = DB::table('plc_initial_install')->where('sn', $info['sn'])->update(['scf' => $info['scf'], 'scm' => $info['scm']]);
            $the = new MqttController;
            return $the->fasong_s($data, 2);
        }

        //正常冲洗时间分和秒
        elseif ($info['type'] == 2) {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['zcf'];
            $data[0]['weizhi'] = 2;
            $data[1]['clientid'] = $info['sn'];
            $data[1]['variable'] = $info['zcm'];
            $data[1]['weizhi'] = 3;
            DB::table('plc_initial_install')->where('sn', $info['sn'])->update(['zcf' => $info['zcf'], 'zcm' => $info['zcm']]);
            $the = new MqttController;
            return $the->fasong_s($data, 2);
        }
        //正常冲洗时间秒

        //泵1加水时间秒
        elseif ($info['type'] == 3) {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['jsm'];
            $data[0]['weizhi'] = 120;
            DB::table('plc_initial_install')->where('sn', $info['sn'])->update(['jsm' => $info['jsm']]);
            $the = new MqttController;
            $the->fasong($data);
            return json_encode(['resutlt' => 0, 'message' => '成功']);  
        }
        //泵2加水时间分和秒
        elseif ($info['type'] == 15) {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['jsf2'];
            $data[0]['weizhi'] = 4;
            $data[1]['clientid'] = $info['sn'];
            $data[1]['variable'] = $info['jsm2'];
            $data[1]['weizhi'] = 5;
            DB::table('plc_initial_install')->where('sn', $info['sn'])->update(['jsf2' => $info['jsf2'], 'jsm2' => $info['jsm2']]);
            $the = new MqttController;
            $the->fasong_s($data, 2);
            return json_encode(['resutlt' => 0, 'message' => '成功']);  
        }
        //泵2加药时间分和秒
        elseif ($info['type'] == 4) {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['jyf'];
            $data[0]['weizhi'] = 6;
            $data[1]['clientid'] = $info['sn'];
            $data[1]['variable'] = $info['jym'];
            $data[1]['weizhi'] = 7;
            DB::table('plc_initial_install')->where('sn', $info['sn'])->update(['jyf' => $info['jyf'], 'jym' => $info['jym']]);
            $the = new MqttController;
            return $the->fasong_s($data, 2);
        }
       
        //清洗等待时间时和分
        elseif ($info['type'] == 5) {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['qxs'];
            $data[0]['weizhi'] = 8;
            $data[1]['clientid'] = $info['sn'];
            $data[1]['variable'] = $info['qxf'];
            $data[1]['weizhi'] = 9;
            DB::table('plc_initial_install')->where('sn', $info['sn'])->update(['qxs' => $info['qxs'], 'qxf' => $info['qxf']]);
            $the = new MqttController;
            return $the->fasong_s($data, 2);
        }
        //清洗等待时间分

        //感应器等待时间(时)和(分)
        elseif ($info['type'] == 6) {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['gys'];
            $data[0]['weizhi'] = 10;
            $data[1]['clientid'] = $info['sn'];
            $data[1]['variable'] = $info['gyf'];
            $data[1]['weizhi'] = 11;
            DB::table('plc_initial_install')->where('sn', $info['sn'])->update(['gys' => $info['gys'], 'gyf' => $info['gyf']]);
            $the = new MqttController;
            return $the->fasong_s($data, 2);
        }
        //感应器等待时间(分)

        //感应器检测延时时间
        elseif ($info['type'] == 7) {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['gym'];
            $data[0]['weizhi'] = 12;
            DB::table('plc_initial_install')->where('sn', $info['sn'])->update(['gym' => $info['gym']]);
            $the = new MqttController;
            return $the->fasong($data);
        }
        //冲洗延时开启时间
        elseif ($info['type'] == 8) {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['cxyskq'];
            $data[0]['weizhi'] = 13;
            DB::table('plc_initial_install')->where('sn', $info['sn'])->update(['cxyskq' => $info['cxyskq']]);
            $the = new MqttController;
            return $the->fasong($data);
        }
        //排水延时关闭时间
        elseif ($info['type'] == 9) {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['psysgb'];
            $data[0]['weizhi'] = 14;
            DB::table('plc_initial_install')->where('sn', $info['sn'])->update(['psysgb' => $info['psysgb']]);
            $the = new MqttController;
            return $the->fasong($data);
        }
        //冲洗气缸开时间
        elseif ($info['type'] == 10) {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['cxqgk'];
            $data[0]['weizhi'] = 15;
            DB::table('plc_initial_install')->where('sn', $info['sn'])->update(['cxqgk' => $info['cxqgk']]);
            $the = new MqttController;
            return $the->fasong($data);
        }
        //冲洗气缸关时间
        elseif ($info['type'] == 11) {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['cxqgg'];
            $data[0]['weizhi'] = 16;
            DB::table('plc_initial_install')->where('sn', $info['sn'])->update(['cxqgg' => $info['cxqgg']]);
            $the = new MqttController;
            return $the->fasong($data);
        }
        //泵1基数
        elseif ($info['type'] == 16) {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['base1'];
            $data[0]['weizhi'] = 119;
            DB::table('plc_initial_install')->where('sn', $info['sn'])->update(['base1' => $info['base1']]);
            $the = new MqttController;
            $the->fasong($data);
            return json_encode(['resutlt' => 0, 'message' => '成功']);  
        }

        //计数启动时间
        elseif ($info['type'] == 17) {
            $data[0]['clientid'] = $info['sn'];
            $data[0]['variable'] = $info['count_start_h'];
            $data[0]['weizhi'] = 122;
            $data[1]['clientid'] = $info['sn'];
            $data[1]['variable'] = $info['count_start_m'];
            $data[1]['weizhi'] = 123;
            DB::table('plc_initial_install')->where('sn', $info['sn'])->update(['count_start_h' => $info['count_start_h'], 'count_start_m' => $info['count_start_m']]);
            $the = new MqttController;
            return $the->fasong_s($data, 2);
        }
    }

   

    //定时发送获取数据指令
    // public function ()

    //测试短信
    public function cesifasong(Request $request)
    {
        $info = $request->all();
        // dd($info);
        $the = new CodeController;
        return $the->sendCode($info);
    }

    //定时启动设置执行
    public function dingshiout()
    {

        $shi = (int) date('H');
        $fen = (int) date('i');
        $data = DB::table('plc_timing')->get();
        $data = objToArr($data);

        //总容量
        $total = DB::table('plc_initial_install')->orderBy('id', 'desc')->limit(1)->value('rl');
        foreach ($data as $k => $v) {
            $shiyong[$k] = DB::table('plc_device_details as a')->leftjoin('plc_general as b', 'a.sn', '=', 'b.sn')->where(['a.sn' => $v['sn']])->get();
            $shiyong = objToArr($shiyong);
            $shiyong = $shiyong[0][0];

            if (($v['qdsA'] == $shi && $v['qdfA'] == $fen) || ($v['qdsB'] == $shi && $v['qdfB'] == $fen) || ($v['qdsC'] == $shi && $v['qdfC'] == $fen) || ($v['qdsD'] == $shi && $v['qdfD'] == $fen) || ($v['qdsE'] == $shi && $v['qdfE'] == $fen) || ($v['qdsF'] == $shi && $v['qdfF'] == $fen) || ($v['qdsG'] == $shi && $v['qdfG'] == $fen) || ($v['qdsH'] == $shi && $v['qdfH'] == $fen) || ($v['qdsI'] == $shi && $v['qdfI'] == $fen) || ($v['qdsJ'] == $shi && $v['qdfJ'] == $fen) || ($v['qdsK'] == $shi && $v['qdfK'] == $fen) || ($v['qdsL'] == $shi && $v['qdfL'] == $fen) || ($v['qdsM'] == $shi && $v['qdfM'] == $fen) || ($v['qdsN'] == $shi && $v['qdfN'] == $fen) || ($v['qdsO'] == $shi && $v['qdfO'] == $fen) || ($v['qdsP'] == $shi && $v['qdfP'] == $fen) || ($v['qdsQ'] == $shi && $v['qdfQ'] == $fen) || ($v['qdsR'] == $shi && $v['qdfR'] == $fen) || ($v['qdsS'] == $shi && $v['qdfS'] == $fen) || ($v['qdsT'] == $shi && $v['qdfT'] == $fen)) {
                if ($shiyong['bxz'] == 1) {
                    $variable = (int) ($shiyong['bsurplus'] - $total * ($shiyong['jyb'] / 100));
                    $variable = $variable < 0 ? 0 : $variable;
                    $data[0]['clientid'] = $v['sn'];
                    $data[0]['variable'] = $variable;
                    $data[0]['weizhi'] = 101;
                    DB::table('plc_device_details')->where('sn', $v['sn'])->update(['bsurplus' => $variable]);
                } elseif ($shiyong['bxz'] == 2) {
                    $variable = (int) ($shiyong['bsurpluss'] - $total * ($shiyong['jybs'] / 100));
                    $variable = $variable < 0 ? 0 : $variable;
                    $data[0]['clientid'] = $v['sn'];
                    $data[0]['variable'] = $variable;
                    $data[0]['weizhi'] = 103;
                    DB::table('plc_device_details')->where('sn', $v['sn'])->update(['bsurpluss' => $variable]);
                }
                // $data[1]['clientid'] = $v['sn'];
                // $data[1]['variable'] = $shiyong['functionnumber'] + 1;
                // $data[1]['weizhi'] = 120;
                $the = new MqttController;
                return $the->fasong($data);
            }
        }
    }
}
