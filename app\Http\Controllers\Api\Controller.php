<?php
namespace App\Http\Controllers\Api;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
class Controller
{
    public function getPe($token)
    {
        $id = substr(decrypts($token),0,-10);
//        dd($id);
        $tokens = encrypts($id);
//        dd(Redis::get($tokens));
        if(Redis::get($tokens)){
            if($id == 1){
                $res = DB::table('plc_admin')->where('sort',$id)->select('id','role')->get()->toArray();
//                dd($res);
            }else{
                $res = DB::table('plc_user')->where('id',$id)->select('id','role')->get()->toArray();
            }

//            dd($res);
            if($res){
                $res = objToArr($res);
//                dd($res[0]['id']);
                return ['id'=>$res[0]['id'],'role'=>$res[0]['role']];

            }else{
                return 0;
            }

        }else{
            return 0;
        }
    }
}