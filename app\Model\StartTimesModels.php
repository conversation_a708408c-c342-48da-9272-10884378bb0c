<?php

namespace App\Model;

use Illuminate\Database\Eloquent\Model;

class StartTimesModels extends Model
{
    //1、关联的数据表
    public $table = 'plc_start_times';

    //2、主键
    public $primaryKey = 'id';

    // 3、允许批量操作的字段
    public $fillable = [
        'sn',
        'start_number',
        'hours',
        'minutes'
    ];

    // 4、是否维护created_at和updated_at字段
    public $timestamps = true;

    /**
     * 字段类型转换
     */
    protected $casts = [
        'start_number' => 'integer',
        'hours' => 'integer',
        'minutes' => 'integer',
    ];

    /**
     * 验证启动编号范围 (1-10)
     */
    public function setStartNumberAttribute($value)
    {
        if ($value < 1 || $value > 10) {
            throw new \InvalidArgumentException('启动编号必须在1-10之间');
        }
        $this->attributes['start_number'] = $value;
    }

    /**
     * 验证小时范围 (0-23)
     */
    public function setHoursAttribute($value)
    {
        if ($value < 0 || $value > 23) {
            throw new \InvalidArgumentException('小时必须在0-23之间');
        }
        $this->attributes['hours'] = $value;
    }

    /**
     * 验证分钟范围 (0-59)
     */
    public function setMinutesAttribute($value)
    {
        if ($value < 0 || $value > 59) {
            throw new \InvalidArgumentException('分钟必须在0-59之间');
        }
        $this->attributes['minutes'] = $value;
    }

    /**
     * 获取格式化的时间字符串
     */
    public function getFormattedTimeAttribute()
    {
        return sprintf('%02d:%02d', $this->hours, $this->minutes);
    }

    /**
     * 根据设备编号查询启动时间
     */
    public static function getByDeviceSn($sn)
    {
        return self::where('sn', $sn)->orderBy('start_number')->get();
    }

    /**
     * 根据启动编号查询
     */
    public static function getByStartNumber($startNumber)
    {
        return self::where('start_number', $startNumber)->get();
    }

    /**
     * 设置启动时间
     */
    public static function setStartTime($sn, $startNumber, $hours, $minutes)
    {
        return self::updateOrCreate(
            ['sn' => $sn, 'start_number' => $startNumber],
            ['hours' => $hours, 'minutes' => $minutes]
        );
    }
}
