<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plc_start_times', function (Blueprint $table) {
            $table->id();
            $table->string('sn')->nullable()->comment('设备唯一编号');
            $table->tinyInteger('start_number')->comment('启动编号 1-10');
            $table->unsignedTinyInteger('hours')->default(0);
            $table->unsignedTinyInteger('minutes')->default(0);
            $table->timestamps();

            $table->index('start_number', 'idx_start_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('start_times');
    }
};
