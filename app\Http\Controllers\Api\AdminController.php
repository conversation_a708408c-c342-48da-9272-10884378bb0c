<?php
namespace App\Http\Controllers\Api;

use App\Http\Controllers\Api\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Crypt;

Class AdminController extends Controller
{
    public function index(Request $request)
    {
        $info = $request->all();
        $token = $request->header('token');
//        dd($token);
        $uid = $this->getPe($token);
//        $export = empty($info['export'])?0:1;
//        dd($uid);
        if(!$token)
        {
            return json_encode(['resutlt'=>1,'message'=>'请登录']);
        }
        
        if($uid['role'] == 1)
        {
            $user = DB::table('plc_admin')->get()->toArray();
            unset($user[0]->password);
            return response()->json(['status'=>0,'msg'=>'查询成功！','data'=>$user]);
        }

        $id = $uid['id'];
//        dd($id);
        $data = DB::table('plc_user')->where(['id'=>$id])->get()->toArray();
        unset($data[0]->password);
//        dd($data);
        return response()->json(['status'=>0,'msg'=>'查询成功！','data'=>$data]);
    }

//    public function edit(Request $request)
//    {
//        $role = $request->input('role');
//        if($role == 1)
//        {
//            $user = DB::table('plc_admin')->get();
//            dd($user);
//            return $user;
//        }
//        $id = $request->input('id');
//
//    }

    public function updates(Request $request)
    {
        $role = $request->input('role');
        $user = $request->except('_token');
        if($role == 1)
        {
            $rule = [
                'account'    => 'required|between:8,18',
                'password'   => 'required|between:6,16|regex:/(^[A-Za-z0-9 ]+$)+/'
            ];

            $msg = [
                'account.required' =>'账号必须输入',
                'account.between' =>'账号长度必需在8-18位之间',
                'password.required' =>'密码必须输入',
                'password.between' =>'密码长度必需在6-16位之间',
                'password.regex' => '密码必须全是字母和数字',
            ];
            $validator = Validator::make($user,$rule,$msg);
            if($validator->fails()){
                return response()->json(['status'=>1,'msg'=>'修改失败！','data'=>$validator->errors()]);
            }
            $res = DB::table('plc_admin')->update(['account'=>$user['account'],'password' =>Crypt::encrypt($user['password'])]);
            if($res)
            {
                return response()->json(['status'=>0,'msg'=>'修改成功！']);
            }else{
                return response()->json(['status'=>1,'msg'=>'修改失败！']);
            }
        }
        $id = $request->input('id');
        $admin = DB::table('plc_user')->where('id',$input['id'])->get();
        $admin = objToArr($admin);
        if(empty($admin))
        {
            return response()->json(['status'=>1,'msg'=>'该账户不存在或已被删除！']);
        }else{
            $admin = $admin[0];
        }
        //判断密码是否正确
        if($input['password'] != Crypt::decrypt($admin['password']))
        {
            return response()->json(['status'=>1,'msg'=>'原密码错误！']);
        }
//        dd($user);
        //进行验证
        $rule = [
            'enterprise' => 'required|between:4,18',
            'user_name'  => 'required|between:2,8',
            'phone'      => 'required|phone',
            'user_names' => 'required|between:2,8',
            'phones'     => 'required|phone',
            'account'    => 'required|between:4,18',
            'password'   => 'required|between:6,18|regex:/(^[A-Za-z0-9 ]+$)+/'
        ];

        $msg = [
            'enterprise.required' =>'企业名必须输入',
            'enterprise.between' =>'企业名长度必需在4-18位之间',
            'user_name.required' =>'联系人1必须输入',
            'phone.required'     => '手机号不能为空',
            'phone.phone'     => '手机号不合法',
            'user_name.between' =>'联系人1长度必需在2-8位之间',
            'user_names.required' =>'联系人2必须输入',
            'phones.required'     => '手机号不能为空',
            'phones.phone'     => '手机号不合法',
            'user_names.between' =>'联系人2长度必需在2-8位之间',
            'account.required' =>'账号必须输入',
            'account.between' =>'账号长度必需在4-18位之间',
            'password.required' =>'密码必须输入',
            'password.between' =>'密码长度必需在6-16位之间',
            'password.regex' => '密码必须全是字母和数字',
        ];
        $validator = Validator::make($user,$rule,$msg);
        if($validator->fails()){
            return response()->json(['status'=>1,'msg'=>'修改失败！','data'=>$validator->errors()]);
        }
        $res = DB::table('plc_user')->where(['id'=>$id])->update([
            'enterprise'=>$user['enterprise'],
            'user_name' =>$user['user_name'],
            'phone'     =>$user['phone'],
            'user_names' =>$user['user_names'],
            'phones' =>$user['phones'],
            'account' =>$user['account'],
            'password' =>Crypt::encrypt($user['password'])]);
        if($res)
        {
            return response()->json(['status'=>0,'msg'=>'修改成功！']);
        }else{
            return response()->json(['status'=>1,'msg'=>'修改失败！']);
        }
    }

    public function update(Request $request)
    {
        $token = $request->header('token');
//        dd($token);
        $uid = $this->getPe($token);
//        $export = empty($info['export'])?0:1;
//        dd($uid);
        if(!$token)
        {
            return json_encode(['resutlt'=>1,'message'=>'请登录']);
        }
//        return 123;
        if($uid['role'] == 1)
        {
            $input = $request->all();
            // if($input['account'])
            // {
            //     $rule = ['account' => 'required|between:4,18'];
            //     $msg = [
            //         'account.required' =>'账号必须输入',
            //         'account.between' =>'账号长度必需在4-18位之间'
            //         ];
            //     $validator = Validator::make($input,$rule,$msg);
            //     if($validator->fails()){
            //         return response()->json(['status'=>1,'msg'=>'修改失败！','data'=>$validator->errors()]);
            //     }
            //     $res = DB::table('plc_admin')->where(['id'=>$uid['id']])->update([
            //         'account'=>$input['account']]);
            //     if($res)
            //     {
            //         return response()->json(['status'=>0,'msg'=>'修改成功！']);
            //     }else{
            //         return response()->json(['status'=>1,'msg'=>'修改失败！']);
            //     }
            // }else
            if($input['password'])
            {
                $rule = ['password'   => 'required|between:8,18'];
                $msg = [
                    'password.required' =>'密码必须输入',
                    'password.between' =>'密码长度必需在8-18位之间'
                ];
                $validator = Validator::make($input,$rule,$msg);
                if($validator->fails()){
                    return response()->json(['status'=>1,'msg'=>'修改失败！','data'=>$validator->errors()]);
                }
                $res = DB::table('plc_admin')->where(['id'=>$uid['id']])->update([
                    'password'=>Crypt::encrypt($input['password'])]);
                if($res)
                {
                    return response()->json(['status'=>0,'msg'=>'修改成功！']);
                }else{
                    return response()->json(['status'=>1,'msg'=>'修改失败！']);
                }
            }
        }
        $input = $request->all();
        // dd($input);
            $rule = [
                'enterprise' => 'required|between:4,18',
                'account' => 'required|between:4,18',
                'password'   => 'required|between:8,18',
                'user_name'=>'required|between:2,8',
                'phone'=>'required|phone',
                'user_names'=>'required|between:2,8',
                'phones'=>'required|phone'
                ];
            $msg = [
                'enterprise.required' =>'企业名必须输入',
                'enterprise.between' =>'企业名长度必需在4-18位之间',
                'account.required' =>'账号必须输入',
                'account.between' =>'账号长度必需在4-18位之间',
                'password.required' =>'新密码必须输入',
                'password.between' =>'新密码长度必需在8-18位之间',
                'user_name.required' =>'联系人1必须输入',
                'phone.required'     => '手机号不能为空',
                'phone.phone'     => '手机号不合法',
                'user_name.between' =>'联系人1长度必需在2-8位之间',
                'user_names.required' =>'联系人2必须输入',
                'phones.required'     => '手机号不能为空',
                'phones.phone'     => '手机号不合法',
                'user_names.between' =>'联系人2长度必需在2-8位之间'
            ];
            $validator = Validator::make($input,$rule,$msg);
            if($validator->fails()){
                return response()->json(['status'=>1,'msg'=>'修改失败！','data'=>$validator->errors()]);
            }
            $res = DB::table('plc_user')->where(['id'=>$input['id']])->update([
                'enterprise'=>$input['enterprise'],
                'account'=>$input['account'],
                'user_names' =>$input['user_names'],
                'phones'=>$input['phones'],
                'password'=>Crypt::encrypt($input['password']),
                'user_name' =>$input['user_name'],
                'phone'=>$input['phone'],
                'one'=>1
                ]);
            if($res)
            {
                return response()->json(['status'=>0,'msg'=>'修改成功！']);
            }else{
                return response()->json(['status'=>1,'msg'=>'修改失败！']);
            }
        
    }
}